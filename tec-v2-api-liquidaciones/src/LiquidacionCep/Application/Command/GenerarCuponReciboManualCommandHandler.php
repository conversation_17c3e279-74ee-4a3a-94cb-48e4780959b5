<?php

namespace App\LiquidacionCep\Application\Command;

use App\Core\Application\Service\BloqueoApplicationService;
use App\LiquidacionCep\Application\Service\LiquidacionApplicationService;
use App\LiquidacionCep\Domain\Repository\LiquidacionRepositoryInterface;

final class GenerarCuponReciboManualCommandHandler extends ContextGenericCommandHandler{

    public function __construct(
        LiquidacionRepositoryInterface $repository, 
        BloqueoApplicationService $bloqueoApplicationService,
        private LiquidacionApplicationService $liquidacionApplicationService,
    ) {
        parent::__construct($repository, $bloqueoApplicationService);
    }

    public function __invoke(GenerarCuponReciboManualCommand $command)
    {
        if (!$command->manual) $this->checkBloqueo($command->personaId);

        $liquidacion = $this->liquidacionApplicationService->generarCuponRecibo(
            $command->items,
            $command->modo,
            $command->pagos,
            $command->personaId,
            $command->notasCredito,
            $command->fechaCalculo,
            $command->preferenciaPagos,
            $command->circuito, 
            $command->sedeId,
            $command->generar,
            $command->manual,
            $command->getAuditableUser()
        );

        return $liquidacion ;
    }
}