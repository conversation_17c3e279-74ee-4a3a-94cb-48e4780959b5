<?php

namespace App\LiquidacionCep\Application\Command;

use App\Core\Application\Command\CommandAuditable;
use App\Core\Application\Command\CommandInterface;

final class GenerarCuponReciboManualCommand extends CommandAuditable
{
    public function __construct(
        public readonly string $personaId,
        public readonly ?string $sedeId,
        public readonly array $items,
        public readonly array $pagos,
        public readonly array $notasCredito,
        public readonly \DateTime $fechaCalculo,
        public readonly string $modo,
        public readonly bool $generar, 
        public readonly string $circuito,
        public readonly ?array $preferenciaPagos = null,
        public readonly ?bool $manual = false,
    ){}
}