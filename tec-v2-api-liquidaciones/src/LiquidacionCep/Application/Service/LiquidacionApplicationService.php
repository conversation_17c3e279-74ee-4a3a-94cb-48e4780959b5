<?php

namespace App\LiquidacionCep\Application\Service;

use Psr\Log\LoggerInterface;
use Symfony\Component\Uid\Uuid;
use App\Core\Domain\Event\EventBus;
use App\Shared\Domain\Model\Sede\Sede;
use App\Shared\Domain\Model\Persona\Cuit;
use App\Shared\Domain\Model\Tarea\Nombre;
use App\Shared\Domain\Model\Tarea\TareaId;
use App\Shared\Domain\Model\Titulo\Codigo;
use App\Shared\Domain\Model\Titulo\Titulo;
use App\Shared\Domain\Model\Tarea\TareaEmb;
use App\Shared\Domain\Model\Modalidad\Sigla;
use App\Shared\Domain\Model\Persona\Persona;
use App\Shared\Domain\Model\Titulo\TituloId;
use App\Shared\Domain\Model\Persona\Apellido;
use App\LiquidacionCep\Domain\Model\Item\Item;
use App\Shared\Domain\Model\Distrito\Distrito;
use App\Shared\Domain\Model\Persona\Documento;
use App\Shared\Domain\Model\Persona\PersonaId;
use App\LiquidacionCep\Domain\Model\Item\ItemId;
use App\LiquidacionCep\Domain\Model\Legajo\Tipo;
use App\Shared\Domain\Model\Categoria\Categoria;
use App\Shared\Domain\Model\Modalidad\Modalidad;
use App\LiquidacionCep\Domain\Event\CepPagoEvent;
use App\Shared\Domain\Model\Distrito\Abreviatura;
use App\LiquidacionCep\Domain\Model\Item\Alicuota;
use App\LiquidacionCep\Domain\Model\Item\Comision;
use App\LiquidacionCep\Domain\Model\Legajo\Legajo;
use App\Shared\Domain\Model\Categoria\CategoriaId;
use App\Shared\Domain\Model\Modalidad\ModalidadId;
use App\Shared\Domain\Model\ObjectIdName\ObjectId;
use App\Shared\Domain\Model\SedeCabecera\Contacto;
use App\Shared\Domain\Model\Tarea\PorcentajeAporte;
use App\LiquidacionCep\Domain\Model\Item\Referencia;
use App\LiquidacionCep\Domain\Model\Legajo\LegajoId;
use App\Shared\Domain\Model\Distrito\NombreDistrito;
use App\Shared\Domain\Model\ObjectIdName\ObjectName;
use App\Shared\Domain\Model\SedeCabecera\NombreSede;
use App\LiquidacionCep\Domain\Model\Item\Descripcion;
use App\LiquidacionCep\Domain\Model\Item\ImporteTotal;
use App\Shared\Domain\Model\Modalidad\NombreModalidad;
use App\Shared\Domain\Model\ObjectIdName\ObjectIdName;
use App\Shared\Domain\Model\SedeCabecera\SedeCabecera;
use App\LiquidacionCep\Domain\Model\Item\Identificador;
use App\LiquidacionCep\Domain\Model\Item\MontoAlicuota;
use App\LiquidacionCep\Domain\Model\Item\MontoComision;
use App\Shared\Domain\Model\LiquidacionBase\MontoTotal;
use App\LiquidacionCep\Domain\Event\CepPagoAnuladoEvent;
use App\LiquidacionCep\Domain\Model\Legajo\NumeroLegajo;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\Fecha;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\Monto;
use App\LiquidacionCep\Domain\Model\Matricula\Matricula;
use App\Shared\Domain\Model\LiquidacionBase\ValidoHasta;
use App\Shared\Domain\Model\SedeCabecera\SedeCabeceraId;
use App\Shared\Domain\ValueObject\EstadosPosiblesLegajo;
use App\Shared\Domain\Model\LiquidacionBase\FechaCalculo;
use App\LiquidacionCep\Domain\Model\Matricula\MatriculaId;
use App\Shared\Domain\Model\LiquidacionBase\LiquidacionId;
use App\LiquidacionCep\Domain\Model\Liquidacion\DistritoId;
use App\Shared\Domain\ValueObject\IdentificadoresConceptos;
use App\LiquidacionCep\Domain\Model\Item\Fecha as ItemFecha;
use App\LiquidacionCep\Domain\Model\Liquidacion\Liquidacion;
use App\Shared\Domain\Model\LiquidacionBase\LiquidacionBase;
use App\Shared\Domain\Model\Persona\Nombre as PersonaNombre;
use App\Shared\Domain\Model\LiquidacionBase\FechaLiquidacion;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\Vencimiento;
use App\LiquidacionCep\Domain\Model\Matricula\CodigoMatricula;
use App\LiquidacionCep\Domain\Model\Matricula\NumeroMatricula;
use App\Core\Application\Service\ApiConnectionServiceInterface;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\TipoContrato;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\LegajoDetalle;
use App\Shared\Application\Service\SharedHttpApplicationService;
use App\Shared\Domain\Model\Categoria\Nombre as CategoriaNombre;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\NumeroContrato;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\LegajoDetalleId;
use App\Shared\Domain\Model\LegajoDetalleTarea\LegajoDetalleTarea;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\MontoDiferenciado;
use App\Shared\Domain\Model\LegajoDetalleTarea\LegajoDetalleTareaId;
use App\Shared\Domain\Model\Persona\DistritoId as PersonaDistritoId;
use App\Shared\Domain\Model\Titulo\Descripcion as TituloDescripcion;
use App\Shared\Application\Service\LiquidacionBaseApplicationService;
use App\Shared\Domain\Model\Distrito\DistritoId as DistritoDistritoId;
use App\Shared\Domain\Model\SedeCabecera\Codigo as SedeCabeceraCodigo;
use App\LiquidacionCep\Domain\Model\Item\Vencimiento as ItemVencimiento;
use App\LiquidacionCep\Domain\Repository\LiquidacionRepositoryInterface;
use App\Shared\Domain\Model\LegajoDetalleTarea\Monto as LegajoDetalleTareaMonto;
use App\Shared\Domain\Model\SedeCabecera\Abreviatura as SedeCabeceraAbreviatura;

final class LiquidacionApplicationService extends LiquidacionBaseApplicationService
{
    public function __construct(
        LiquidacionRepositoryInterface $repository, 
        SharedHttpApplicationService $httpService,
        LoggerInterface $logger,
        private ApiConnectionServiceInterface $apiConnection,
        private EventBus $asyncBus,
    ) {
        parent::__construct($httpService, $repository, $logger);
    }

    /**
     *  Metodo que permite obtener la deuda/importe
     * 
     * @param $items items de deuda que se quieren recalcular
     * @param $id identificador del detalle
     * @param $args data extra para el circuito
     * 
     * @return $deuda retorna un array con la deuda/importe 
     */
    public function obtenerDeuda($items, $id,$fecha, array $args = [])
    {
        $deuda = [];    
        
        // Validar que existan items de Legajos
        if (count($items) == 0)
        {
            $objetoId = isset($args['objetoId']) ? $args['objetoId'] : null;

            if (!$objetoId) return $deuda; 
            
            $sedeId = $args['sedeId'];
    
            $sede = $sedeId ? $this->httpService->obtenerSede($sedeId) : null;
            $concepto = $this->httpService->findConceptoByKey(IdentificadoresConceptos::CEP);
            $legajo = $this->httpService->obtenerLegajo($objetoId);

            $data = $this->getDataCep($legajo, $sede);
            //$data['persona'] = $this->getDataPersona($persona);
            $data['concepto'] = $this->getDataConcepto($concepto[0]);

            $deuda[] = (object) $data;
        } else {
            $args["fecha"] = $fecha;

            $deuda = $this->validarDeuda($deuda, $items, $id, $args);
        }

        return $deuda ;
    }

    public function calcularLiquidacion($items, $fecha_para_calcular = new \DateTime('now'), array $args = [])
    {
        return $items;
    }

    public function validarLiquidacion(LiquidacionBase $liquidacion): array
    {
        return [] ;
    }

    /**
    * Metodo que valida la dueda , comparando la preLiquidacion con la deuda y devuelve los elementos de deuda para luego hacer el recalculo
    * @param $deuda deuda de la persona que se obtiene del servicio cuotas 
    * @param $items
    * @param $persona_id identificador de la persona a la cual se le calcula la preLiquidacion 
    */
    public function validarDeuda($deuda, $items, $id, array $args = [])
    {
        $idLegajosATimbrar = [];
        $deuda_validada = [];
        foreach ($items as $item)
        {
            switch ($item->concepto->tipo_item->key) 
            {
                case IdentificadoresConceptos::CEP:
                    // Recalculo y validacion (fecha, sedes, etc)
                    $itemRecalculado = $this->recalcular($item, $args);

                    if ($this->buscarIdRepetido($idLegajosATimbrar, $item->identificador)) throw new BadRequestHttpException("IDs repetidos");
                    $idLegajosATimbrar[] = $item->identificador;

                    $deuda_validada[] = $itemRecalculado;
                    break;
                case IdentificadoresConceptos::TIPO_OTRO_CONCEPTO:
                case IdentificadoresConceptos::TIPO_BONIFICACION:
                    $deuda_validada[] = $this->generarConceptoOtro($item);
                    break;
            }
        }

        return $deuda_validada ;
    }

    public function validarItems($liquidacion, $orden_pago, &$nota_credito, &$monto_nota, $circuito)
    {
        $valido = true;
        
        //validar que los items de la liquidacion , esten en condiciones
        foreach($liquidacion->getItems() as $item)
        {
            $tipo_concepto_key = $item->getConcepto()->getTipoItem()->getKey()->getValue();
            switch ($tipo_concepto_key) {
                case IdentificadoresConceptos::CEP:
                    $valido = $this->validarItem($item);
                    break;
            }

            //sino estan en condiciones, generar nota credito
            if (!$valido)
            {
                $nota_credito = true;
                $monto_nota = $orden_pago->monto_total;
                return $valido;
            }
        }

        return $valido;
    }

    public function validarItem(Item $item)
    {
        try {
            $legajo = $this->httpService->obtenerLegajo($item->getIdentificador()->getValue());
            return $legajo->puede_procesar_cep;
        } catch (\Throwable $th) {
            $this->logger->error("Error al validar item: ".$th->__toString());
            return false;
        }
    }


    public function recalcular($item, $args) 
    {
        $legajo = $this->httpService->obtenerLegajo($item->identificador);

        if ($legajo->cep_cumplido) throw new BadRequestHttpException("CEP cumplimentado para el legajo #".$legajo->numero_legajo);

        $concepto = $this->httpService->findConceptoByKey(IdentificadoresConceptos::CEP);

        $sedeId = $args['sedeId'];
        $sede = $sedeId ? $this->httpService->obtenerSede($sedeId) : null;

        $data = $this->getDataCep($legajo, $sede, @$args["fecha"]);
        //$data['persona'] = $this->getDataPersona($persona);
        $data['concepto'] = $this->getDataConcepto($concepto[0]);

        return (object) $data;
    }

    public function lanzarEventos(LiquidacionBase $liquidacion, ?string $reciboId = null, ?string $ordenPagoId = null)
    {
        $this->logger->info("Se procede a lanzar eventos...");
        /** @var Item $item */
        foreach($liquidacion->getItems() as $item)
        {
            switch($item->getConcepto()->getKey()->getValue()) {
                case IdentificadoresConceptos::CEP:
                    $this->logger->info("Record evento...");
                    $liquidacion->recordDomainEvent($this->eventoCep($item, $liquidacion, $reciboId, $ordenPagoId));
                    break; 
                default:
                    break;
            }
        }
        // Publicar eventos
        $this->asyncBus->publish(...$liquidacion->pullDomainEvents());
    }

    public function validacionPuedeReversa($recibo)
    {
        $liquidacion = $this->repository->findByOrdenPagoId($recibo->orden_pago);

        if ($liquidacion == null) return false;

        $check = true;
        foreach ($liquidacion->getItems() as $item) 
        {
            if ($item->getConcepto()->getKey()->getValue() == IdentificadoresConceptos::CEP)
            {
                $puedeAnular = $this->httpService->puedeAnularCep($item->getIdentificador()->getValue(), $item->getReferencia()->getValue());
                if ($puedeAnular->result == false) {
                    $check = false;
                    break;
                }
            }
        }

        return $check;
    }

    public function lanzarEventosDesacritacion($recibo, $reversa)
    {
        $liquidacion = $this->repository->findByOrdenPagoId($recibo->orden_pago);

        $events = null;
        foreach ($liquidacion->getItems() as $item) 
        {
            if ($item->getConcepto()->getKey()->getValue() == IdentificadoresConceptos::CEP)
            {
                $events[] = $this->eventoCepPagoAnulado($item, $liquidacion, $recibo->id);
            }
        }

        // Publicar eventos
        if ($events) $this->asyncBus->publish(...$events); 
    }

    public function eventoCepPagoAnulado($item,$liquidacion,$reciboId){

        return new CepPagoAnuladoEvent(
            $liquidacion->getId(),
            $item->getReferencia()->getValue(),
            $item->getIdentificador()->getValue(), 
            $reciboId
        );
    } 

    private function eventoCep(Item $item, LiquidacionBase $liquidacion, ?string $reciboId, ?string $ordenPagoId)
    {
        $fecha = new \DateTime('NOW');

        $event = new CepPagoEvent(
            $liquidacion->getId(), 
            $item->getReferencia()->getValue(), // ID a aplicar
            $fecha->format('Y-m-d H:i:s'),
            $item->getImporteTotal()->getValue(),
            $item->getIdentificador()->getValue(), // legajo
            $liquidacion->getSede() ? $liquidacion->getSede()->getId()->getValue() : null,
            $item->getDescripcion() ? $item->getDescripcion()->getValue() : null,
            $liquidacion->getCreatedBy(),
            $reciboId,
            $ordenPagoId,
        );

        $this->logger->info("Evento CEP generado: ".json_encode($event->toPrimitives()));

        return $event;
    }

    public function obtenerObjetoAuxiliar($item, bool $array = false)
    {
        return null;
    }


    public function crearItemLiquidacion($item) 
    {
        // Generico para conceptos, y especifico para cep 
        $it = array(
            'id' => new ItemId(Uuid::v4()->__toString()),
            'importeTotal' => new ImporteTotal($item->importe_total),
            'descripcion' => new Descripcion($item->descripcion),
            'fecha' => isset($item->fecha) ? new ItemFecha($item->fecha) : null,
            'concepto' => $this->createTipoConcepto($item->concepto),
        );

        if ($item->concepto->tipo_item->key == IdentificadoresConceptos::CEP)
        {
            $detalles = [];
            foreach ($item->objeto->detalles as $d) {
                
                $tareas = null;
                foreach ($d->tareas as $t) {
                    $tareas[] = new LegajoDetalleTarea(
                        new LegajoDetalleTareaId($t->id),
                        new LegajoDetalleTareaMonto(@$t->monto),
                        new TareaEmb(
                            new TareaId($t->tarea->id),
                            new Nombre(@$t->tarea->nombre),
                            new PorcentajeAporte(@$t->tarea->porcentaje_aporte),
                            @$t->tarea->cep_porcentaje_diferenciado
                        )
                    );
                }

                $detalles[] = new LegajoDetalle(
                    new LegajoDetalleId($d->id),
                    new Fecha(new \DateTime($d->fecha)),
                    new Vencimiento(new \DateTime($d->vencimiento)),
                    new Monto($d->monto),
                    new TipoContrato($d->tipo_contrato),
                    new MontoDiferenciado(@$d->monto_diferenciado),
                    new NumeroContrato(@$d->numero_contrato),
                    $tareas
                );
            }

            $itCep = [
                'identificador' => new Identificador($item->identificador),
                'referencia' => new Referencia($item->referencia),
                'vencimiento' => new ItemVencimiento($item->vencimiento),
                'objeto' => new Legajo(
                    new LegajoId($item->objeto->id),
                    new Tipo($item->objeto->tipo),
                    new NumeroLegajo($item->objeto->numero_legajo),
                    isset($item->objeto->persona) ? new Persona(
                        new PersonaId(@$item->objeto->persona->id),
                        new PersonaNombre(@$item->objeto->persona->nombre),
                        new Documento(@$item->objeto->persona->documento),
                        new Apellido(@$item->objeto->persona->apellido),
                        new PersonaDistritoId($item->objeto->persona->distrito_id),
                        new Cuit($item->objeto->persona->cuit),
                    ) : null,
                    isset($item->objeto->matricula) ? new Matricula(
                        new MatriculaId($item->objeto->matricula->id),
                        new NumeroMatricula(@$item->objeto->matricula->numero),
                        new CodigoMatricula(@$item->objeto->matricula->codigo),
                        isset($item->objeto->matricula->titulo) ? new Titulo(
                            new TituloId($item->objeto->matricula->titulo->id),
                            new TituloDescripcion(@$item->objeto->matricula->titulo->descripcion),
                            new Codigo(@$item->objeto->matricula->titulo->codigo),
                            isset($item->objeto->matricula->titulo->rubro) ? new ObjectIdName(
                                new ObjectId($item->objeto->matricula->titulo->rubro->id),
                                new ObjectName($item->objeto->matricula->titulo->rubro->nombre),
                            ) : null
                        ) : null,
                        isset($item->objeto->matricula->institucion) ? new ObjectIdName(
                            new ObjectId($item->objeto->matricula->institucion->id),
                            new ObjectName(@$item->objeto->matricula->institucion->nombre),
                        ) : null,
                        isset($item->objeto->matricula->categoria) ? new Categoria(
                            new CategoriaId($item->objeto->matricula->categoria->id),
                            new CategoriaNombre(@$item->objeto->matricula->categoria->nombre),
                            isset($item->objeto->matricula->categoria->tipo_categoria) ? new ObjectIdName(
                                new ObjectId($item->objeto->matricula->categoria->tipo_categoria->id),
                                new ObjectName(@$item->objeto->matricula->categoria->tipo_categoria->nombre),
                            ) : null
                        ) : null,
                    ) : null,
                    isset($item->objeto->distrito) ? new Distrito(
                        new DistritoDistritoId($item->objeto->distrito->id),
                        new NombreDistrito($item->objeto->distrito->nombre),
                        new Abreviatura($item->objeto->distrito->abreviatura),
                        isset($item->objeto->distrito->sede_cabecera) ? new SedeCabecera(
                            new SedeCabeceraId($item->objeto->distrito->sede_cabecera->id),
                            new NombreSede($item->objeto->distrito->sede_cabecera->nombre),
                            new SedeCabeceraAbreviatura($item->objeto->distrito->sede_cabecera->abreviatura),
                            new Contacto($item->objeto->distrito->sede_cabecera->contacto),
                            isset($item->objeto->distrito->sede_cabecera->partido) ? new ObjectIdName(
                                new ObjectId($item->objeto->distrito->sede_cabecera->partido->id),
                                new ObjectName(@$item->objeto->distrito->sede_cabecera->partido->nombre),
                            ) : null,
                            new SedeCabeceraCodigo($item->objeto->distrito->sede_cabecera->codigo),
                        ) : null
                    ) : null,
                    isset($item->objeto->modalidad) ? new Modalidad(
                        new ModalidadId(@$item->objeto->modalidad->id),
                        new NombreModalidad(@$item->objeto->modalidad->nombre),
                        new Sigla(@$item->objeto->modalidad->sigla),
                        @$item->objeto->modalidad->privada
                    ) : null,
                    isset($item->objeto->rubro) ? new ObjectIdName(
                        new ObjectId($item->objeto->rubro->id),
                        new ObjectName(@$item->objeto->rubro->nombre),
                    ) : null,
                    $detalles
                ),
            ];
            $itCep['metadata'] = isset($item->objeto) ? json_encode($item->objeto) : null;

            $it = array_merge($it, $itCep);
        }

        return $it ;
    }

    public function crearItemLiquidacionArray($item)
    {
        // Generico para conceptos, y especifico para cep 
        $it = [
            'id' => isset($item->id) ? $item->id : Uuid::v4()->__toString(),
            'importe_total' => $item->importe_total, //$montoAlicuota + $montoComision,
            'descripcion' => $item->descripcion,
            'fecha' => isset($item->fecha) ? $item->fecha : null,
            'concepto' => $this->createTipoConceptoArray($item->concepto),
        ];

        if ($it['concepto']['tipo_item']['key'] == IdentificadoresConceptos::CEP)
        {
            $objeto = $this->getDataLegajo($item->objeto);
            $itCep = [
                'alicuota' => isset($item->alicuota) ? $item->alicuota : null,
                'monto_alicuota' => isset($item->monto_alicuota) ? $item->monto_alicuota : null,
                'comision' => isset($item->comision) ? $item->comision : null,
                'monto_comision' => isset($item->monto_comision) ? $item->monto_comision : null,
                'prepago' => isset($item->prepago) ? $item->prepago : null,
                'mismo_partido' => isset($item->mismo_partido) ? $item->mismo_partido : null,
                'identificador' => isset($item->identificador) ? $item->identificador : null,
                'vencimiento' => isset($item->vencimiento) ? $item->vencimiento : null,
                'referencia' => isset($item->referencia) ? $item->referencia : null,
                'objeto' => $objeto,
                'metadata' => json_encode($objeto),
            ];  

            $it = array_merge($it, $itCep);
        }  

        return $it ;
    }

    public function crearObjetoLiquidacion(
        LiquidacionId $liquidacionId, Persona $persona, ?string $distrito, 
        ValidoHasta $validoHasta, MontoTotal $montoTotal,
        array $items, array $objetosAuxiliares, ?Sede $sede = null, ?string $createdBy = null
    )
    {
        // Reemplazar la fecha de validez al circuito
        $validoHasta = null;
        $checkConceptoCep = false;
        foreach ($items as $i) {
            if ($i['concepto']->getTipoItem()->getKey()->getValue() == IdentificadoresConceptos::CEP)
            {
                $checkConceptoCep = true;
                if (!$validoHasta) $validoHasta = new ValidoHasta($i['vencimiento']->getValue());
                elseif ($i['vencimiento']->getValue() < $validoHasta->getValue()) $validoHasta = new ValidoHasta($i['vencimiento']->getValue());
            }          
        }

        if (!$validoHasta) $validoHasta = new ValidoHasta((new \DateTime())->modify('+1 month'));
        //if (!$checkConceptoCep) throw new BadRequestHttpException("No se detectó un concepto CEP.");

        /*
        $id_matriculas = [] ;
        $matriculas = [] ;
        foreach ($items as $it) 
        {
            if ($it['concepto']['tipo_item']['key'] == IdentificadoresConceptos::CEP)
            {
                $legajoDetalle = $this->httpService->obtenerLegajoDetalle($it["identificador"]);
                $matricula = $legajoDetalle->legajo->matricula;
                
                if(!in_array($matricula->id, $id_matriculas))
                {
                    $id_matriculas[] = $matricula->id ;
                    //$mat = $this->httpService->obtenerMatricula($matricula->id);
                
                    $matriculas[] = [
                        'id' => $matricula->id,
                        'codigo' => $matricula->codigo,
                        'tituloId' => $matricula->titulo->id,
                        'idSistemaAnterior' => @$matricula->id_sistema_anterior
                    ];
                }
            }
        }
        */

        $liquidacion = Liquidacion::create(
            $liquidacionId,
            $persona,
            new DistritoId($distrito),
            $items,
            new FechaCalculo(new \DateTime('NOW')),
            new FechaLiquidacion(new \DateTime('NOW')),
            $validoHasta, //new ValidoHasta($fecha_valido instanceof DateTime ? $fecha_valido : new DateTime($fecha_valido)),
            null, 
            $montoTotal,
            $sede
        );

        return $liquidacion;
    }

    public function crearArrayLiquidacion(
        array $persona, ?string $distrito, 
        \DateTime $validoHasta, float $montoTotal,
        array $items, array $objetosAuxiliares, ?Sede $sede = null
    )
    {

        $validoHasta = null;
        $id_matriculas = [];
        $matriculas = [];
        $checkConceptoCep = false;
        foreach ($items as $it) 
        {
            if ($it['concepto']['tipo_item']['key'] == IdentificadoresConceptos::CEP)
            {
                $checkConceptoCep = true;
                $legajo = $this->httpService->obtenerLegajo($it["identificador"]);
                $matricula = $legajo->matricula;
                
                if(!in_array($matricula->id, $id_matriculas))
                {
                    $id_matriculas[] = $matricula->id ;
                
                    $matriculas[] = [
                        'id' => $matricula->id,
                        'codigo' => $matricula->codigo,
                        'tituloId' => $matricula->titulo->id,
                        'idSistemaAnterior' => @$matricula->id_sistema_anterior
                    ];
                }

                // Reemplazar la fecha de validez al circuito
                if (!$validoHasta) $validoHasta = $it['vencimiento'];
                elseif ($it['vencimiento'] < $validoHasta) $validoHasta = $it['vencimiento'];
            }
        }

        //if (!$checkConceptoCep) throw new BadRequestHttpException("No se detectó un concepto CEP.");
        //$validoHasta = new \DateTime();

        $liquidacion = [
            'persona' => $persona,
            'distrito' => $distrito,
            'sede' => $sede,
            'items' => $items,
            'fechaCalculo' => new \DateTime('NOW'),
            'fechaLiquidacion' => new \DateTime('NOW'),
            'validoHasta' => $validoHasta,
            'ordenPagoId' => null, 
            'montoTotal' => $montoTotal,
            'matriculas' => $matriculas,
            //'sede' => $sede
        ];

        return $liquidacion;
    }

    // ############################ FUNCIONES PROPIAS DEL CIRCUITO ############################

    private function buscarIdRepetido($array, $id) 
    {
        foreach ($array as $v) {
            if ($v == $id) return true;
        }
    }

    private function getDataCep($legajo, $sede, $fecha = null)
    {
        $fecha = (!$fecha) ? new \DateTime('NOW') : $fecha;

        $descripcion = "CEP LEGAJO #".$legajo->numero_legajo;

        $modalidadId = $legajo->modalidad->id;
        $monto = $this->calcularMontoTotal($legajo);
        $montoDiferenciado = $this->calcularMontoDiferenciadoTotal($legajo);
        $montoCepAbonado = $this->calcularMontoCepAbonado($legajo);

        $todasTareasDiferenciadas = @$legajo->contiene_todas_las_tareas_diferenciadas;
        $montoCep = $this->httpService->obtenerMontoCEP($modalidadId, $monto, $montoDiferenciado, $fecha->format('Y-m-d H:i:s'), $todasTareasDiferenciadas);

        // Obtener fecha hasta del periodo vigente de la modalidad
        $vencimientoModalidad = $this->httpService->obtenerVencimientoModalidad($legajo->modalidad->id);
        $vencimientoModalidad->setTime(23,59,59);

        $vencimiento90dias = clone $fecha; 
        $vencimiento90dias->modify('+ 90 days')->setTime(23,59,59);
        $vencimiento = ($vencimiento90dias < $vencimientoModalidad) ? $vencimiento90dias : $vencimientoModalidad; 

        $ultimoDiaAnio = new \DateTime();
        $ultimoDiaAnio->modify('last day of December this year')->setTime(23,59,59);

        if ($vencimiento > $ultimoDiaAnio) $vencimiento = $ultimoDiaAnio;

        // Revisar feriados
        $flag = false;
        $feriados_anio = $this->httpService->obtenerFeriadoByAnio($vencimiento->format('Y'));
        while (!$flag) 
        {
            if ($this->isFinDeSemana($vencimiento) || $this->isFeriado($vencimiento->format('Y-m-d'), $feriados_anio))
            {
                $vencimiento->modify('-1 day');
            } else {
                $flag = true ;
            }
        }

        $dataLegajo = $this->getDataLegajo($legajo);
       
        return [
            'objeto' => $dataLegajo,
            'identificador' => $legajo->id,
            'referencia' => Uuid::v4()->__toString(),
            'descripcion' => $descripcion,
            'vencimiento' => $vencimiento,
            'sede' => $sede,
            'monto_diferenciado' => $montoDiferenciado,
            'monto_cep_abonado' => $montoCepAbonado,
            'monto_cep_total' => $montoCep,
            'importe_total' => $montoCep - $montoCepAbonado,
        ];
    }

    private function getDataLegajo($legajo)
    {
        $detalles = [];
        foreach ($legajo->detalles as $d) {

            $tareas = null;

            foreach ($d->tareas as $t) {
                $tareas[] = (object) [
                    'id' => $t->id,
                    'cep_calculo_diferenciado' => @$t->cep_calculo_diferenciado,
                    'monto' => @$t->monto,
                    'tarea' => (object) [
                        'id' => $t->tarea->id,
                        'nombre' => @$t->tarea->nombre,
                        'cep_porcentaje_diferenciado' => @$t->tarea->cep_porcentaje_diferenciado,
                        'porcentaje_aporte' => @$t->tarea->porcentaje_aporte,
                    ]
                ];
            }

            $detalles[] = (object) [
                'id' => $d->id,
                'fecha' => $d->fecha,
                'vencimiento' => $d->vencimiento,
                'tipo_contrato' => $d->tipo_contrato,
                'numero_contrato' => @$d->numero_contrato,
                'monto' => $d->monto,
                'monto_diferenciado' => @$d->monto_diferenciado,
                'tareas' => $tareas
            ];
        }

        if (isset($legajo->distrito))
        {
            $distrito = (object) [
                'id' => $legajo->distrito->id,
                'nombre' => @$legajo->distrito->nombre,
                'abreviatura' => @$legajo->distrito->abreviatura,
                'sede_cabecera' => (isset($legajo->distrito->sede_cabecera)) ? (object) [
                    'id' => $legajo->distrito->sede_cabecera->id,
                    'nombre' => @$legajo->distrito->sede_cabecera->nombre,
                    'abreviatura' => @$legajo->distrito->sede_cabecera->abreviatura,
                    'contacto' => @$legajo->distrito->sede_cabecera->contacto,
                    'codigo' => @$legajo->distrito->sede_cabecera->codigo,
                    'partido' => (isset($legajo->distrito->sede_cabecera->partido)) ? (object) [
                        'id' => $legajo->distrito->sede_cabecera->partido->id,
                        'nombre' => @$legajo->distrito->sede_cabecera->partido->nombre,
                    ] : null,
                ] : null,
            ];
        } else { $distrito = null; }
        
        if (isset($legajo->comitentes))
        {
            foreach ($legajo->comitentes as $c) {
                $comitentes[] = (object) [
                    'id' => $c->id,
                    'locador' => @$c->locador,
                    'apellido_nombre' => @$c->apellido_nombre,
                    'nombre' => @$c->nombre,
                    'apellido' => @$c->apellido,
                    'documento' => @$c->documento,
                    'cuit' => @$c->cuit,
                    'manual' => @$c->manual,
                    'fisico' => @$c->fisico,
                ];
            }
        } else { $comitentes = null; }

        return (object) [
            'id' => $legajo->id,
            'numero_legajo' => $legajo->numero_legajo,
            'tipo' => $legajo->tipo,
            'modalidad' => (object) [
                'id' => $legajo->modalidad->id,
                'nombre' => $legajo->modalidad->nombre,
                'sigla' => $legajo->modalidad->sigla,
                'privada' => $legajo->modalidad->privada,
            ],
            'persona' => (object) [
                'id' => $legajo->persona->id,
                'nombre' => $legajo->persona->nombre,
                'apellido' => $legajo->persona->apellido,
                'cuit' => $legajo->persona->cuit,
                'documento' => $legajo->persona->documento,
                'distrito_id' => @$legajo->persona->distrito_id,
            ],
            'matricula' => (object) [
                'id' => $legajo->matricula->id,
                'numero' => @$legajo->matricula->numero,
                'codigo' => @$legajo->matricula->codigo,
                'titulo' => (object) [
                    'id' => $legajo->matricula->titulo->id,
                    'descripcion' => @$legajo->matricula->titulo->descripcion,
                    'codigo' => @$legajo->matricula->titulo->codigo,
                    'rubro' => (object) [
                        'id' => $legajo->matricula->titulo->rubro->id,
                        'nombre' => @$legajo->matricula->titulo->rubro->nombre,
                    ]
                ],
                'institucion' => (object) [
                    'id' => $legajo->matricula->institucion->id,
                    'nombre' => $legajo->matricula->institucion->nombre,
                ],
                'categoria' => (object) [
                    'id' => $legajo->matricula->categoria->id,
                    'nombre' => @$legajo->matricula->categoria->nombre,
                    'tipo_categoria' => (object) [
                        'id' => $legajo->matricula->categoria->tipo_categoria->id,
                        'nombre' => @$legajo->matricula->categoria->tipo_categoria->nombre,
                    ]
                ],
            ],
            'rubro' => (object) [
                'id' => $legajo->rubro->id,
                'nombre' => $legajo->rubro->nombre,
            ],
            'distrito' => $distrito,
            'localidad' => isset($legajo->inmuebles) ? (object) [
                'id' => $legajo->inmuebles[0]->localidad->id,
                'nombre' => $legajo->inmuebles[0]->localidad->nombre,
                'partido' => (object) [
                    'id' => $legajo->inmuebles[0]->localidad->partido->id,
                    'nombre' => $legajo->inmuebles[0]->localidad->partido->nombre,
                ],
            ] : null,
            'inmuebles' => isset($legajo->inmuebles) ? $legajo->inmuebles : null,
            'detalles' => $detalles,
            'detalle_vigente' => isset($legajo->detalle_vigente) ? $legajo->detalle_vigente : null,
            'comitentes' => $comitentes,
        ];
    }

    private function getDataPersona($persona)
    {
        return (object) [
            'id' => $persona->id,
            'nombre' => $persona->nombre,
            'apellido' => $persona->apellido,
            'documento' => $persona->documento,
            'cuit' => $persona->cuit,
            'distrito' => $persona->distrito,
        ];
    }

    private function getDataConcepto($concepto)
    {
        return (object) [
            'id' => $concepto->id,
            'nombre' => $concepto->nombre,
            'key' => $concepto->key,
            'signo' => $concepto->signo,
            'tipo_item' => (object) [
                'id' => $concepto->tipo_item->id,
                'nombre' => $concepto->tipo_item->nombre,
                'key' => $concepto->tipo_item->key,
                'tipo' => $concepto->tipo_item->tipo,
            ]
        ];
    }

    public function generarConceptoOtro($item)
    {    
        $concepto = $this->httpService->findConceptoByKey($item->concepto->key)[0];

        $configuracionMonto = $concepto->gestion_monto->configuracion;
        $valor = isset($concepto->gestion_monto->valor) ? $concepto->gestion_monto->valor : null; 

        $importe_total = $item->importe_total;

        if ($configuracionMonto == 'manual_fijo') $importe_total = $valor;

        $item = (object) [
            'id' => Uuid::v4()->__toString(),
            'descripcion' => isset($item->descripcion) ? $item->descripcion : $concepto->nombre,
            'importe_total' => $importe_total,
            'fecha' => isset($item->fecha) ? $item->fecha : null,
            'concepto' => (object)[
                'id' => $concepto->id ,
                'nombre' => $concepto->nombre,
                'key' => $concepto->key,
                'signo' => $concepto->signo,
                'gestion_monto' => (object)[
                    'configuracion' => $configuracionMonto,
                    'valor' => $valor,
                ],
                'tipo_item' => (object)[
                   'id' => $concepto->tipo_item->id,
                   'nombre' => $concepto->tipo_item->nombre,
                   'key' => $concepto->tipo_item->key,
                   'tipo' => $concepto->tipo_item->tipo     
                ]
            ],
        ];

        return $item;
    }


    private function calcularMontoTotal($legajo): float
    {
        $monto = 0;
        foreach ($legajo->detalles as $d) 
        {
            if (!($d->estado_id == EstadosPosiblesLegajo::ANULADO['id']))
            {
                $monto += (property_exists($d, 'monto')) ? $d->monto : 0; 
            }
        }

        return $monto;
    }

    private function calcularMontoDiferenciadoTotal($legajo): float
    {
        $monto = 0;
        foreach ($legajo->detalles as $d) 
        {
            if (!($d->estado_id == EstadosPosiblesLegajo::ANULADO['id']))
            {
                $monto += (property_exists($d, 'monto_diferenciado')) ? $d->monto_diferenciado : 0; 
            }
        }

        return $monto;
    }

    private function calcularMontoCepAbonado($legajo): float
    {
        $monto = 0;
        foreach ($legajo->ceps as $c) 
        {
            if (!$c->anulado)
            {
                $monto += (property_exists($c, 'importe')) ? $c->importe : 0; 
            }
        }

        return $monto;
    }

}