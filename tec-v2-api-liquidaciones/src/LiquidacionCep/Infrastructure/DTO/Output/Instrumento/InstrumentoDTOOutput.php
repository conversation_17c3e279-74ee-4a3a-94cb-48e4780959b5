<?php

namespace App\LiquidacionCep\Infrastructure\DTO\Output\Instrumento;

use App\LiquidacionCep\Infrastructure\DTO\Output\Concepto\TipoItemDTOOutput;
use Symfony\Component\Serializer\Annotation\Groups;

final class InstrumentoDTOOutput {

    #[Groups(['item:calculo:input', 'operacionPago:validar:input','item:calculo:input','item:recibo-cupon:input','item:recibo-cupon:output'])]
    private string $id ;

    #[Groups(['item:calculo:input', 'operacionPago:validar:input','item:calculo:input','item:recibo-cupon:input','item:recibo-cupon:output'])]
    private string $nombre;

    #[Groups(['item:calculo:input', 'operacionPago:validar:input','item:calculo:input','item:recibo-cupon:input','item:recibo-cupon:output'])]
    private string $key;
    
    #[Groups(['item:calculo:input', 'operacionPago:validar:input','item:calculo:input','item:recibo-cupon:input','item:recibo-cupon:output'])]
    private ?int $signo = null ;

    #[Groups(['item:calculo:input', 'operacionPago:validar:input','item:calculo:input','item:recibo-cupon:input','item:recibo-cupon:output'])]
    private TipoItemDTOOutput $tipoItem;

    public function __construct(
        string $id,
        string $nombre,
        string $key,
        ?int $signo,
        TipoItemDTOOutput $tipoItem
    ){
        $this->id = $id ;
        $this->nombre = $nombre;
        $this->key = $key ;
        $this->signo = $signo;
        $this->tipoItem = $tipoItem ;
    }

    
    /**
     * Get the value of id
     */ 
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set the value of id
     *
     * @return  self
     */ 
    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Get the value of nombre
     */ 
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * Set the value of nombre
     *
     * @return  self
     */ 
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;

        return $this;
    }

    /**
     * Get the value of key
     */ 
    public function getKey()
    {
        return $this->key;
    }

    /**
     * Set the value of key
     *
     * @return  self
     */ 
    public function setKey($key)
    {
        $this->key = $key;

        return $this;
    }

    /**
     * Get the value of tipoItem
     */ 
    public function getTipoItem()
    {
        return $this->tipoItem;
    }

    /**
     * Set the value of tipoItem
     *
     * @return  self
     */ 
    public function setTipoItem($tipoItem)
    {
        $this->tipoItem = $tipoItem;

        return $this;
    }

    /**
     * Get the value of signo
     */ 
    public function getSigno()
    {
        return $this->signo;
    }

    /**
     * Set the value of signo
     *
     * @return  self
     */ 
    public function setSigno($signo)
    {
        $this->signo = $signo;

        return $this;
    }
}