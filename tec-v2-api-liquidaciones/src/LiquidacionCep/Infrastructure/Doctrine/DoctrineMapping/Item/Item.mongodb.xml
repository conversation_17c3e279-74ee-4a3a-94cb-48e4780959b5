<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mongo-mapping 
    xmlns="http://doctrine-project.org/schemas/odm/doctrine-mongo-mapping"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://doctrine-project.org/schemas/odm/doctrine-mongo-mapping
                    http://doctrine-project.org/schemas/odm/doctrine-mongo-mapping.xsd">

    <embedded-document name="App\LiquidacionCep\Domain\Model\Item\Item">
        <id field-name="id" type="string" strategy="NONE"/>

        <embed-one field="vencimiento" target-document="App\LiquidacionCep\Domain\Model\Item\Vencimiento" nullable="true"/>
        <embed-one field="fecha" target-document="App\LiquidacionCep\Domain\Model\Item\Fecha" nullable="true"/>
        <embed-one field="descripcion" target-document="App\LiquidacionCep\Domain\Model\Item\Descripcion" nullable="true" />
        <embed-one field="importeTotal" target-document="App\LiquidacionCep\Domain\Model\Item\ImporteTotal" />
        <embed-one field="identificador" target-document="App\LiquidacionCep\Domain\Model\Item\Identificador" nullable="true" />
        <embed-one field="referencia" target-document="App\LiquidacionCep\Domain\Model\Item\Referencia" nullable="true" />
        <embed-one field="concepto" target-document="App\Shared\Domain\Model\Concepto\Concepto" />
        <embed-one field="importeTotal" target-document="App\LiquidacionCep\Domain\Model\Item\ImporteTotal" />
        <field field-name="metadata" type="string" nullable="true" />
        
        <embed-one field="objeto" target-document="App\LiquidacionCep\Domain\Model\Legajo\Legajo" nullable="true" />

    </embedded-document>
</doctrine-mongo-mapping>    
