<?php


namespace App\Shared\Application\Service;

use DateTime;
use Symfony\Component\Uid\Uuid;
use App\Shared\Domain\Model\Persona\Persona;
use App\Core\Application\Service\ApiConnectionServiceInterface;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

final class SharedHttpApplicationService {
 

    public function __construct(
        private ApiConnectionServiceInterface $apiConnection,
    ) {
    }


    public function obtenerConcepto($conceptoId){
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-rel-finanzas/conceptos/$conceptoId",
            "GET"
        );

        return json_decode($request->getContent());
    }

    public function findConceptoByKey($key) {
        
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-rel-finanzas/conceptos?key=$key",
            "GET"
        );

        return json_decode($request->getContent()); 
    }

    public function findInstrumentoByKey($key) {
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-rel-finanzas/instrumentos?key=$key",
                "GET"
            );

            return json_decode($request->getContent()); 
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el instrumento $key");
        }
    }

    public function findCausaById($causaId){
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-causas/causas/$causaId",
            "GET"
        );
        
        return json_decode($request->getContent());
    }

    public function obtenerMatricula($matriculaId){
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-matriculados/matriculas/$matriculaId",
            "GET"
        );

        return json_decode($request->getContent());
    }

    public function obtenerCuota($cuotaId){
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-cuotas/cuotas/$cuotaId",
            "GET"
        );

        return json_decode($request->getContent());
    }

    public function newOrdenPago($ordenPago){
        $request = $this->apiConnection::httpRequest(
            'http://tec-api-recaudaciones/orden_pagos',
            'POST',
            $ordenPago 

        );

        return json_decode($request->getContent());
    }

    public function newReciboNota($body){
        
        $request = $this->apiConnection::httpRequest(
            'http://tec-api-recibos/comprobantes/generar',
            'POST',
            $body

        );

        return json_decode($request->getContent());
       
    }

    public function generarNotaCredito($body){
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-recibos/comprobante/notacredito",
            "POST",
            $body
        );

        return json_decode($request->getContent());
    }

    public function updateNotaCredito($body,$nota_id){
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-recibos/nota_creditos/$nota_id",
            "PUT",
            $body
        );

        return json_decode($request->getContent());
    }

    public function updateRecibo($body,$id){
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-recibos/recibos/$id",
            "PUT",
            $body
        );

        return json_decode($request->getContent());
    }

    public function obtenerDeudaPersona($personaId,$fecha_visible_hasta){

        $fecha_visible_desde = (new DateTime())->format('Y-m-d H:i:s');
        
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-cuotas/cuotas?pagination=false&persona.id=$personaId&paga=0&fecha_visible[strictly_before]=$fecha_visible_desde",
            "GET"
        );

        return json_decode($request->getContent());
    }
   

    public function obtenerPlanVigente($fecha){
        
        $body = [
            "fecha_calculo" => $fecha->format('Y-m-d H:i:s') 
        ];
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-cuotas/plan_pagos/vigente/calculado",
            "POST",
            $body
        );

        return json_decode($request->getContent());
    }

    public function obtenerPlanByAnio($anio){
        
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-cuotas/plan_pagos?pagination=false&anio=$anio",
            "GET"
        );

        return json_decode($request->getContent());
    }

    public function validarOrden($operacionPago,$ordenPago){
        $body =[
            "operacion_pago" => $operacionPago,
            "orden_pago" => $ordenPago
        ];

        $request = $this->apiConnection::httpRequest(
            "http://tec-api-recaudaciones/orden_pagos/operacionPago/validar",
            "POST",
            $body
        );
        return json_decode($request->getContent());
    }

    public function deleteOrden($orden_id){
        
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-recaudaciones/orden_pagos/$orden_id",
            "DELETE"
        );
        return json_decode($request->getContent());
    }


    public function actualizarOrden($orden_id,$body){

        $request = $this->apiConnection::httpRequest(
            "http://tec-api-recaudaciones/orden_pagos/$orden_id",
            "PUT",
            $body
        );

        return json_decode($request->getContent());
    }

    public function obtenerEstadoMatricula($matriculaId){
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-matriculados/estados_matriculas/$matriculaId",
            "GET"
        );

        return json_decode($request->getContent());
    }

    public function obtenerPersonaOComitente($identificador)
    {
        // Si es uuid es una persona, sino es un cuit y es un comitente
        if (Uuid::v4()->isValid($identificador)) {
            $persona = $this->obtenerPersona($identificador);
            $persona->type = Persona::MATRICULADO;
        } else {
            $persona = $this->obtenerComitente($identificador);
            $persona->nombre = $persona->fisico ? $persona->nombre : '';
            $persona->apellido = $persona->fisico ? $persona->apellido : $persona->apellido_nombre; 
            $persona->distrito = null;
            $persona->type = Persona::COMITENTE;
        }

        return $persona;
    }

    public function obtenerPersona($personaId)
    {
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-matriculados/personas/$personaId",
            "GET"
        );

        return json_decode($request->getContent());
    }

    public function obtenerComitente($cuit)
    {
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-fichas/comitentes/findByCuit/$cuit",
                "GET"
            );

            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el comitente con cuit: $cuit");
        }
    }

    public function findCausaByMatricula($matriculaId){
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-causas/causas?matricula.id=$matriculaId",
            "GET"
        );

        return json_decode($request->getContent());
    }

    public function obtenerCuotasResumen($filtros){

        $url = "http://tec-api-cuotas/cuotas?pagination=false&" ;
        //se le aplican los filtros a la url 
        $url .= http_build_query($filtros);

        $request = $this->apiConnection::httpRequest(
            $url,
            "GET"
        );

        return json_decode($request->getContent());
    }


    public function obtenerNotaCreditoById($notaId){
        
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-recibos/comprobantes/$notaId",
            "GET"
        );

        return json_decode($request->getContent());
    }
    
    public function obtenerFeriadoByAnio($anio){
        
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-generales/feriados?anio=$anio",
            "GET"
        );

        return json_decode($request->getContent());
    }

    public function obtenerLegajo($legajoId) {
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-fichas/legajos/$legajoId",
                "GET"
            );
    
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el legajo $legajoId");
        }
    }

    public function obtenerLegajoDetalle($legajoDetalleId) {
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-fichas/legajo_detalles/$legajoDetalleId",
                "GET"
            );
    
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el legajo $legajoDetalleId");
        }
    }

    public function puedeAnularCep($legajoId, $cepId) {
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-fichas/legajos/puede_anular_cep",
                "POST",
                [
                    "cep_id" => "$cepId",
                    "legajo_id" => "$legajoId"
                ]
            );
    
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el legajo $legajoId");
        }
    }

    public function puedeAnularTimbrado($legajoDetalleId, $timbradoId) {
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-fichas/legajo_detalles/puede_anular_timbrado",
                "POST",
                [
                    "timbrado_id" => "$timbradoId",
                    "legajo_detalle_id" => "$legajoDetalleId"
                ]
            );
    
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el legajo detalle $legajoDetalleId");
        }
    }

    public function obtenerSede($sedeId) {
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-generales/sedes/$sedeId",
                "GET"
            );
    
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener la sede $sedeId");
        }
    }

    public function accionesPosiblesSede($sedeId) {
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-generales/sedes/$sedeId/acciones_posibles",
                "GET"
            );
    
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener la sede $sedeId");
        }
    }

    public function obtenerVencimientoModalidad($modalidadId): \DateTime
    {
        $result = $this->getVencimientoModalidad($modalidadId);

        $vencimiento = new \DateTime($result->vencimiento);

        return $vencimiento;
    }

    private function getVencimientoModalidad($modalidadId)
    {
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-fichas/modalidads/$modalidadId/obtener_vencimiento", 
                "GET"
            );
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el vencimiento del periodo vigente de la modalidad.");
        }

        return json_decode($request->getContent());
    }
    
    public function obtenerMontoCEP(string $modalidadId, float $monto, ?float $montoDiferenciado = 0, ?string $fecha = null, ?bool $todasTareasDiferenciadas = false)
    {
        $request = $this->getMontoCEP($modalidadId, $monto, $montoDiferenciado, $fecha, $todasTareasDiferenciadas);

        return $request->result;
    }

    private function getMontoCEP(string $modalidadId, float $monto, ?float $montoDiferenciado = 0, ?string $fecha = null, ?bool $todasTareasDiferenciadas = false)
    {
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-fichas/modalidads/calcular_monto_cep", 
                "POST",
                [
                    'modalidad_id' => $modalidadId,
                    'fecha' => $fecha,
                    'monto' => $monto,
                    'monto_diferenciado' => $montoDiferenciado, 
                    'todas_tareas_diferenciadas' => $todasTareasDiferenciadas, 
                ]
            );
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el monto del CEP");
        }

        return json_decode($request->getContent());
    }

    public function obtenerCodigoBarra($body){
       try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-agentes/codigo_barra/generar", 
                "POST",
                $body
            );
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el Codigo de Barra");
        }

        return json_decode($request->getContent());
    }

    public function findOrdenPagoById($ordenId){
        
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-recaudaciones/orden_pagos/$ordenId",
                "GET"
            );
    
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener la orden $ordenId");
        }
    }

    public function findDistritoById($distritoId){
        
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-generales/distritos/$distritoId",
                "GET"
            );
    
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el distrito $distritoId");
        }
    }

    public function findCuentaById($cuentaId)
    {    
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-conciliaciones/cuentas/$cuentaId",
                "GET"
            );
    
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener la cuenta $cuentaId");
        }
    }


    public function obtenerReciboById($reciboId){
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-recibos/comprobantes/$reciboId",
                "GET"
            );

            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el recibo $reciboId");
        }    
    }


    public function newReciboReversa($body){
        try {
            $request = $this->apiConnection::httpRequest(
                'http://tec-api-recibos/reversa/generar',
                'POST',
                $body

            );

            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al crear un reversa de pago");
        }    
    }

    public function getCategoriaById($id){
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-rel-matriculas/categorias/$id",
                "GET"
            );
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener la categoria $id");
        }    
    }

    public function getTituloById($tituloId){
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-rel-matriculas/titulos/$tituloId",
                "GET"
            );

            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el titulo $tituloId");
        }    
    }

    public function desacreditarNotaCredito($id){
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-recibos/comprobante/notacredito/$id",
                'POST',
                []

            );

            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al desacreditar la nota");
        }    
    }

}
