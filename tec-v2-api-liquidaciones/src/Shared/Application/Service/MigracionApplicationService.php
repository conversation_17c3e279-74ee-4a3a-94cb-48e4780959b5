<?php


namespace App\Shared\Application\Service;

use Psr\Log\LoggerInterface;
use App\Shared\Domain\Model\Persona\Cuit;
use App\Shared\Domain\Model\Tarea\Nombre;
use App\Shared\Domain\Model\Tarea\TareaId;
use App\Shared\Domain\Model\Titulo\Codigo;
use App\Shared\Domain\Model\Titulo\Titulo;
use App\Shared\Domain\Model\Tarea\TareaEmb;
use App\Shared\Domain\Model\Modalidad\Sigla;
use App\Shared\Domain\Model\Persona\Persona;
use App\Shared\Domain\Model\Titulo\TituloId;
use App\Shared\Domain\Model\Persona\Apellido;
use App\Shared\Domain\Model\Distrito\Distrito;
use App\Shared\Domain\Model\Persona\Documento;
use App\Shared\Domain\Model\Persona\PersonaId;
use App\LiquidacionCep\Domain\Model\Legajo\Tipo;
use App\Shared\Domain\Model\Categoria\Categoria;
use App\Shared\Domain\Model\Modalidad\Modalidad;
use App\Shared\Domain\Model\Distrito\Abreviatura;
use App\LiquidacionCep\Domain\Model\Legajo\Legajo;
use App\Shared\Domain\Model\Categoria\CategoriaId;
use App\Shared\Domain\Model\Modalidad\ModalidadId;
use App\Shared\Domain\Model\ObjectIdName\ObjectId;
use App\Shared\Domain\Model\SedeCabecera\Contacto;
use App\Shared\Domain\Event\CompletarMetadataEvent;
use App\Shared\Domain\Model\Tarea\PorcentajeAporte;
use App\LiquidacionCep\Domain\Model\Item\Referencia;
use App\LiquidacionCep\Domain\Model\Legajo\LegajoId;
use App\Shared\Domain\Model\Distrito\NombreDistrito;
use App\Shared\Domain\Model\ObjectIdName\ObjectName;
use App\Shared\Domain\Model\SedeCabecera\NombreSede;
use App\Shared\Domain\Model\Modalidad\NombreModalidad;
use App\Shared\Domain\Model\ObjectIdName\ObjectIdName;
use App\Shared\Domain\Model\SedeCabecera\SedeCabecera;
use App\LiquidacionCep\Domain\Model\Item\Identificador;
use App\LiquidacionCep\Domain\Model\Legajo\NumeroLegajo;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\Fecha;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\Monto;
use App\LiquidacionCep\Domain\Model\Matricula\Matricula;
use App\Shared\Domain\Model\SedeCabecera\SedeCabeceraId;
use App\Shared\Domain\Model\Tarea\Nombre as TareaNombre;
use App\LiquidacionCep\Domain\Model\Matricula\MatriculaId;
use App\Shared\Domain\Model\Titulo\Codigo as TituloCodigo;
use App\Shared\Domain\Model\Persona\Nombre as PersonaNombre;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\Vencimiento;
use App\LiquidacionCep\Domain\Model\Matricula\CodigoMatricula;
use App\LiquidacionCep\Domain\Model\Matricula\NumeroMatricula;
use App\Core\Application\Service\ApiConnectionServiceInterface;
use App\Liquidacion\Infrastructure\Doctrine\DoctrineLiquidacionRepository as InfrastructureDoctrineDoctrineLiquidacionRepository;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\TipoContrato;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\LegajoDetalle;
use App\Shared\Domain\Model\Categoria\Nombre as CategoriaNombre;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\NumeroContrato;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\LegajoDetalleId;
use App\Shared\Domain\Model\LegajoDetalleTarea\LegajoDetalleTarea;
use App\LiquidacionTimbrado\Domain\Model\Legajo\Tipo as LegajoTipo;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use App\LiquidacionCep\Domain\Model\LegajoDetalle\MontoDiferenciado;
use App\Shared\Domain\Model\LegajoDetalleTarea\LegajoDetalleTareaId;
use App\Shared\Domain\Model\Persona\DistritoId as PersonaDistritoId;
use App\Shared\Domain\Model\Titulo\Descripcion as TituloDescripcion;
use App\Shared\Domain\Model\Distrito\DistritoId as DistritoDistritoId;
use App\Shared\Domain\Model\SedeCabecera\Codigo as SedeCabeceraCodigo;
use App\LiquidacionTimbrado\Domain\Model\Legajo\Legajo as LegajoLegajo;
use App\LiquidacionCep\Domain\Model\Item\Vencimiento as ItemVencimiento;
use App\LiquidacionTimbrado\Domain\Model\Legajo\LegajoId as LegajoLegajoId;
use App\LiquidacionCep\Infrastructure\Doctrine\DoctrineLiquidacionRepository;
use App\LiquidacionTimbrado\Domain\Model\Item\MatriculaId as ItemMatriculaId;
use App\Shared\Domain\Model\LegajoDetalleTarea\Monto as LegajoDetalleTareaMonto;
use App\Shared\Domain\Model\SedeCabecera\Abreviatura as SedeCabeceraAbreviatura;
use App\LiquidacionTimbrado\Domain\Model\Legajo\NumeroLegajo as LegajoNumeroLegajo;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\Fecha as LegajoDetalleFecha;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\Monto as LegajoDetalleMonto;
use App\LiquidacionTimbrado\Domain\Model\Matricula\Matricula as MatriculaMatricula;
use App\LiquidacionTimbrado\Domain\Model\Matricula\MatriculaId as MatriculaMatriculaId;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\Vencimiento as LegajoDetalleVencimiento;
use App\LiquidacionTimbrado\Domain\Model\Matricula\CodigoMatricula as MatriculaCodigoMatricula;
use App\LiquidacionTimbrado\Domain\Model\Matricula\NumeroMatricula as MatriculaNumeroMatricula;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\TipoContrato as LegajoDetalleTipoContrato;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\LegajoDetalle as LegajoDetalleLegajoDetalle;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\NumeroContrato as LegajoDetalleNumeroContrato;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\LegajoDetalleId as LegajoDetalleLegajoDetalleId;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\MontoDiferenciado as LegajoDetalleMontoDiferenciado;
use App\LiquidacionTimbrado\Infrastructure\Doctrine\DoctrineLiquidacionRepository as DoctrineDoctrineLiquidacionRepository;

final class MigracionApplicationService {

    public function __construct(
        private ApiConnectionServiceInterface $apiConnection,
        private DoctrineLiquidacionRepository $repoC,
        private DoctrineDoctrineLiquidacionRepository $repoT,
        private InfrastructureDoctrineDoctrineLiquidacionRepository $repoM,
        private SharedHttpApplicationService $httpService,
        private LoggerInterface $logger,
    ){}


    public function generarMetadata(CompletarMetadataEvent $event)
    {
        $this->logger->info("[".$event->getCircuito()."]"." Embebiendo metadata en la liquidacion: ".$event->getLiquidacionId().". Orden: ".$event->getId().". Objeto: ".$event->getIdentificador());
        $circuito = $event->getCircuito();
        $itemLiq = $event->getItemLiquidacionId();
        $identificador = $event->getIdentificador();
        $liquidacionId = $event->getLiquidacionId();

        try {
            if($circuito != 'matriculas'){
                if ($circuito == 'cep')
                {
                    $liq = $this->repoC->ofId($liquidacionId);
                    $std = $this->getLegajo($identificador);
                    $metadata = $this->getDataLegajo($std);
                    $objeto = $this->getObjetoLegajo($std);
                } else if ($circuito == 'timbrados')
                {
                    $liq = $this->repoT->ofId($liquidacionId);
                    $std = $this->getLegajoDetalle($identificador);
                    $metadata = $this->getDataLegajoDetalle($std);
                    $objeto = $this->getObjetoLegajoDetalle($std);
                }
    
                $metadata = json_encode($metadata);

                foreach ($liq->getItems() as $it) {
                    if ($it->getId() == $itemLiq) {
                        $it->setMetadata($metadata);
                        $it->setObjeto($objeto);
                        break;
                    }
                }
            }else {
                $liq = $this->repoM->ofId($liquidacionId);
                foreach($liq->getItems() as $it){
                    $objeto = $this->getDataMatriculas($it);
                    //$it->setObjeto($objeto);
                    $it->setMetadata(json_encode($objeto));
                }
            }

            if ($circuito == 'cep')
            {
                $this->repoC->persist($liq);
                $this->repoC->flush();
            } else if ($circuito == 'timbrados')
            {
                $this->repoT->persist($liq);
                $this->repoT->flush();
            }else if($circuito == 'matriculas'){
                $this->repoM->persist($liq);
                $this->repoM->flush();
            }
            
        } catch (\Throwable $th) {
            $this->logger->error("[".$event->getCircuito()."]"." Error al setear metadata del objeto: ".$event->getIdentificador());
            $this->logger->error($th->__toString());
        }
    }
 
    private function getLegajo($uuid)
    {
        $request = $this->apiConnection::httpRequest(
            "http://tec-api-fichas/legajos/$uuid", 
            'GET',
        );

        return json_decode($request->getContent());
    }

    private function getLegajoDetalle($uuid)
    {
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-fichas/legajo_detalles/$uuid", 
                'GET',
            );
    
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            dump($th->__toString());
        }
        
    }

    private function getDataLegajoDetalle($legajoDetalle)
    {
        $tareas = null;

        if (isset($legajoDetalle->tareas)) 
        {
            foreach ($legajoDetalle->tareas as $t) {
                $tareas[] = (object) [
                    'id' => $t->id,
                    'cep_calculo_diferenciado' => @$t->cep_calculo_diferenciado,
                    'monto' => @$t->monto,
                    'tarea' => (object) [
                        'id' => $t->tarea->id,
                        'nombre' => @$t->tarea->nombre,
                        'cep_porcentaje_diferenciado' => @$t->tarea->cep_porcentaje_diferenciado,
                        'porcentaje_aporte' => @$t->tarea->porcentaje_aporte,
                    ]
                ];
            }
        }
        

        if (isset($legajoDetalle->legajo->distrito))
        {
            $distrito = (object) [
                'id' => $legajoDetalle->legajo->distrito->id,
                'nombre' => @$legajoDetalle->legajo->distrito->nombre,
                'abreviatura' => (isset($legajoDetalle->legajo->distrito->abreviatura)) ? $legajoDetalle->legajo->distrito->abreviatura : null,
                'sede_cabecera' => (object) [
                    'id' => $legajoDetalle->legajo->distrito->sede_cabecera->id,
                    'nombre' => @$legajoDetalle->legajo->distrito->sede_cabecera->nombre,
                    'abreviatura' => @$legajoDetalle->legajo->distrito->sede_cabecera->abreviatura,
                    'contacto' => @$legajoDetalle->legajo->distrito->sede_cabecera->contacto,
                    'codigo' => @$legajoDetalle->legajo->distrito->sede_cabecera->codigo,
                    'partido' => (object) [
                        'id' => $legajoDetalle->legajo->distrito->sede_cabecera->partido->id,
                        'nombre' => @$legajoDetalle->legajo->distrito->sede_cabecera->partido->nombre,
                    ],
                ],
            ];
        } else { $distrito = null; }


        return (object) [
            'id' => $legajoDetalle->id,
            'fecha' => $legajoDetalle->fecha,
            'vencimiento' => $legajoDetalle->vencimiento,
            'monto' => @$legajoDetalle->monto,
            'monto_diferenciado' => @$legajoDetalle->monto_diferenciado,
            'tipo_contrato' => @$legajoDetalle->tipo_contrato,
            'numero_contrato' => isset($legajoDetalle->numero_contrato) ? $legajoDetalle->numero_contrato : 0,
            'tareas' => $tareas,
            'legajo' => (object) [
                'id' => $legajoDetalle->legajo->id,
                'numero_legajo' => $legajoDetalle->legajo->numero_legajo,
                'tipo' => $legajoDetalle->legajo->tipo,
                'modalidad' => isset($legajoDetalle->legajo->modalidad) ? (object) [
                    'id' => $legajoDetalle->legajo->modalidad->id,
                    'nombre' => @$legajoDetalle->legajo->modalidad->nombre,
                    'sigla' => @$legajoDetalle->legajo->modalidad->sigla,
                    'privada' => @$legajoDetalle->legajo->modalidad->privada,
                ] : null,
                'persona' => isset($legajoDetalle->legajo->persona) ? (object) [
                    'id' => $legajoDetalle->legajo->persona->id,
                    'nombre' => $legajoDetalle->legajo->persona->nombre,
                    'apellido' => $legajoDetalle->legajo->persona->apellido,
                    'cuit' => @$legajoDetalle->legajo->persona->cuit,
                    'documento' => $legajoDetalle->legajo->persona->documento,
                    'distrito_id' => @$legajoDetalle->legajo->persona->distrito_id,
                ] : null,
                'matricula' => isset($legajoDetalle->legajo->matricula) ? (object) [
                    'id' => $legajoDetalle->legajo->matricula->id,
                    'numero' => @$legajoDetalle->legajo->matricula->numero,
                    'codigo' => @$legajoDetalle->legajo->matricula->codigo,
                    'titulo' => (object) [
                        'id' => $legajoDetalle->legajo->matricula->titulo->id,
                        'descripcion' => @$legajoDetalle->legajo->matricula->titulo->descripcion,
                        'codigo' => @$legajoDetalle->legajo->matricula->titulo->codigo,
                        'rubro' => (object) [
                            'id' => $legajoDetalle->legajo->matricula->titulo->rubro->id,
                            'nombre' => @$legajoDetalle->legajo->matricula->titulo->rubro->nombre,
                        ]
                    ],
                    'institucion' => (object) [
                        'id' => $legajoDetalle->legajo->matricula->institucion->id,
                        'nombre' => @$legajoDetalle->legajo->matricula->institucion->nombre,
                    ],
                    'categoria' => (object) [
                        'id' => $legajoDetalle->legajo->matricula->categoria->id,
                        'nombre' => @$legajoDetalle->legajo->matricula->categoria->nombre,
                        'tipo_categoria' => (object) [
                            'id' => $legajoDetalle->legajo->matricula->categoria->tipo_categoria->id,
                            'nombre' => @$legajoDetalle->legajo->matricula->categoria->tipo_categoria->nombre,
                        ]
                    ],
                ] : null,
                'rubro' => isset($legajoDetalle->legajo->rubro) ? (object) [
                    'id' => $legajoDetalle->legajo->rubro->id,
                    'nombre' => $legajoDetalle->legajo->rubro->nombre,
                ] : null,
                'distrito' => $distrito,
                'localidad' => isset($legajoDetalle->legajo->inmuebles) ? (object) [
                    'id' => $legajoDetalle->legajo->inmuebles[0]->localidad->id,
                    'nombre' => $legajoDetalle->legajo->inmuebles[0]->localidad->nombre,
                    'partido' => (object) [
                        'id' => $legajoDetalle->legajo->inmuebles[0]->localidad->partido->id,
                        'nombre' => $legajoDetalle->legajo->inmuebles[0]->localidad->partido->nombre,
                    ],
                ] : null,
                'inmuebles' => isset($legajoDetalle->legajo->inmuebles) ? $legajoDetalle->legajo->inmuebles : null,
            ],
        ];
    }

    private function getDataLegajo($legajo)
    {
        $detalles = [];
        foreach ($legajo->detalles as $d) {

            $tareas = null;

            foreach ($d->tareas as $t) {
                $tareas[] = (object) [
                    'id' => $t->id,
                    'cep_calculo_diferenciado' => @$t->cep_calculo_diferenciado,
                    'monto' => @$t->monto,
                    'tarea' => (object) [
                        'id' => $t->tarea->id,
                        'nombre' => @$t->tarea->nombre,
                        'cep_porcentaje_diferenciado' => @$t->tarea->cep_porcentaje_diferenciado,
                        'porcentaje_aporte' => @$t->tarea->porcentaje_aporte,
                    ]
                ];
            }

            $detalles[] = (object) [
                'id' => $d->id,
                'fecha' => $d->fecha,
                'vencimiento' => $d->vencimiento,
                'tipo_contrato' => $d->tipo_contrato,
                'numero_contrato' => @$d->numero_contrato,
                'monto' => $d->monto,
                'monto_diferenciado' => @$d->monto_diferenciado,
                'tareas' => $tareas
            ];
        }

        if (isset($legajo->distrito))
        {
            $distrito = (object) [
                'id' => $legajo->distrito->id,
                'nombre' => @$legajo->distrito->nombre,
                'abreviatura' => @$legajo->distrito->abreviatura,
                'sede_cabecera' => (isset($legajo->distrito->sede_cabecera)) ? (object) [
                    'id' => $legajo->distrito->sede_cabecera->id,
                    'nombre' => @$legajo->distrito->sede_cabecera->nombre,
                    'abreviatura' => @$legajo->distrito->sede_cabecera->abreviatura,
                    'contacto' => @$legajo->distrito->sede_cabecera->contacto,
                    'codigo' => @$legajo->distrito->sede_cabecera->codigo,
                    'partido' => (isset($legajo->distrito->sede_cabecera->partido)) ? (object) [
                        'id' => $legajo->distrito->sede_cabecera->partido->id,
                        'nombre' => @$legajo->distrito->sede_cabecera->partido->nombre,
                    ] : null,
                ] : null,
            ];
        } else { $distrito = null; }
        

        return (object) [
            'id' => $legajo->id,
            'numero_legajo' => $legajo->numero_legajo,
            'tipo' => $legajo->tipo,
            'modalidad' => (object) [
                'id' => $legajo->modalidad->id,
                'nombre' => $legajo->modalidad->nombre,
                'sigla' => $legajo->modalidad->sigla,
                'privada' => $legajo->modalidad->privada,
            ],
            'persona' => (object) [
                'id' => $legajo->persona->id,
                'nombre' => $legajo->persona->nombre,
                'apellido' => $legajo->persona->apellido,
                'cuit' => $legajo->persona->cuit,
                'documento' => $legajo->persona->documento,
                'distrito_id' => @$legajo->persona->distrito_id,
            ],
            'matricula' => (object) [
                'id' => $legajo->matricula->id,
                'numero' => @$legajo->matricula->numero,
                'codigo' => @$legajo->matricula->codigo,
                'titulo' => (object) [
                    'id' => $legajo->matricula->titulo->id,
                    'descripcion' => @$legajo->matricula->titulo->descripcion,
                    'codigo' => @$legajo->matricula->titulo->codigo,
                    'rubro' => (object) [
                        'id' => $legajo->matricula->titulo->rubro->id,
                        'nombre' => @$legajo->matricula->titulo->rubro->nombre,
                    ]
                ],
                'institucion' => (object) [
                    'id' => $legajo->matricula->institucion->id,
                    'nombre' => $legajo->matricula->institucion->nombre,
                ],
                'categoria' => (object) [
                    'id' => $legajo->matricula->categoria->id,
                    'nombre' => @$legajo->matricula->categoria->nombre,
                    'tipo_categoria' => (object) [
                        'id' => $legajo->matricula->categoria->tipo_categoria->id,
                        'nombre' => @$legajo->matricula->categoria->tipo_categoria->nombre,
                    ]
                ],
            ],
            'rubro' => (object) [
                'id' => $legajo->rubro->id,
                'nombre' => $legajo->rubro->nombre,
            ],
            'distrito' => $distrito,
            'localidad' => isset($legajo->inmuebles) ? (object) [
                'id' => $legajo->inmuebles[0]->localidad->id,
                'nombre' => $legajo->inmuebles[0]->localidad->nombre,
                'partido' => (object) [
                    'id' => $legajo->inmuebles[0]->localidad->partido->id,
                    'nombre' => $legajo->inmuebles[0]->localidad->partido->nombre,
                ],
            ] : null,
            'inmuebles' => isset($legajo->inmuebles) ? $legajo->inmuebles : null,
            'detalles' => $detalles,
            'detalle_vigente' => isset($legajo->detalle_vigente) ? $legajo->detalle_vigente : null,
        ];
    }

    private function getObjetoLegajo($legajo)
    {
        $detalles = [];
        foreach ($legajo->detalles as $d) {
            
            $tareas = null;
            foreach ($d->tareas as $t) {
                $tareas[] = new LegajoDetalleTarea(
                    new LegajoDetalleTareaId($t->id),
                    new LegajoDetalleTareaMonto(@$t->monto),
                    new TareaEmb(
                        new TareaId($t->tarea->id),
                        new Nombre(@$t->tarea->nombre),
                        new PorcentajeAporte(@$t->tarea->porcentaje_aporte),
                        @$t->tarea->cep_porcentaje_diferenciado
                    )
                );
            }

            $detalles[] = new LegajoDetalle(
                new LegajoDetalleId($d->id),
                new Fecha(new \DateTime($d->fecha)),
                new Vencimiento(new \DateTime($d->vencimiento)),
                new Monto($d->monto),
                new TipoContrato($d->tipo_contrato),
                new MontoDiferenciado(@$d->monto_diferenciado),
                new NumeroContrato(@$d->numero_contrato),
                $tareas
            );
        }

        return new Legajo(
            new LegajoId($legajo->id),
            new Tipo($legajo->tipo),
            new NumeroLegajo($legajo->numero_legajo),
            isset($legajo->persona) ? new Persona(
                new PersonaId(@$legajo->persona->id),
                new PersonaNombre(@$legajo->persona->nombre),
                new Documento(@$legajo->persona->documento),
                new Apellido(@$legajo->persona->apellido),
                new PersonaDistritoId(@$legajo->persona->distrito_id),
                new Cuit($legajo->persona->cuit),
            ) : null,
            isset($legajo->matricula) ? new Matricula(
                new MatriculaId($legajo->matricula->id),
                new NumeroMatricula(@$legajo->matricula->numero),
                new CodigoMatricula(@$legajo->matricula->codigo),
                isset($legajo->matricula->titulo) ? new Titulo(
                    new TituloId($legajo->matricula->titulo->id),
                    new TituloDescripcion(@$legajo->matricula->titulo->descripcion),
                    new Codigo(@$legajo->matricula->titulo->codigo),
                    isset($legajo->matricula->titulo->rubro) ? new ObjectIdName(
                        new ObjectId($legajo->matricula->titulo->rubro->id),
                        new ObjectName($legajo->matricula->titulo->rubro->nombre),
                    ) : null
                ) : null,
                isset($legajo->matricula->institucion) ? new ObjectIdName(
                    new ObjectId($legajo->matricula->institucion->id),
                    new ObjectName(@$legajo->matricula->institucion->nombre),
                ) : null,
                isset($legajo->matricula->categoria) ? new Categoria(
                    new CategoriaId($legajo->matricula->categoria->id),
                    new CategoriaNombre(@$legajo->matricula->categoria->nombre),
                    isset($legajo->matricula->categoria->tipo_categoria) ? new ObjectIdName(
                        new ObjectId($legajo->matricula->categoria->tipo_categoria->id),
                        new ObjectName(@$legajo->matricula->categoria->tipo_categoria->nombre),
                    ) : null
                ) : null,
            ) : null,
            isset($legajo->distrito) ? new Distrito(
                new DistritoDistritoId($legajo->distrito->id),
                new NombreDistrito($legajo->distrito->nombre),
                new Abreviatura($legajo->distrito->abreviatura),
                isset($legajo->distrito->sede_cabecera) ? new SedeCabecera(
                    new SedeCabeceraId($legajo->distrito->sede_cabecera->id),
                    new NombreSede($legajo->distrito->sede_cabecera->nombre),
                    new SedeCabeceraAbreviatura($legajo->distrito->sede_cabecera->abreviatura),
                    new Contacto($legajo->distrito->sede_cabecera->contacto),
                    isset($legajo->distrito->sede_cabecera->partido) ? new ObjectIdName(
                        new ObjectId($legajo->distrito->sede_cabecera->partido->id),
                        new ObjectName(@$legajo->distrito->sede_cabecera->partido->nombre),
                    ) : null,
                    new SedeCabeceraCodigo($legajo->distrito->sede_cabecera->codigo),
                ) : null
            ) : null,
            isset($legajo->modalidad) ? new Modalidad(
                new ModalidadId(@$legajo->modalidad->id),
                new NombreModalidad(@$legajo->modalidad->nombre),
                new Sigla(@$legajo->modalidad->sigla),
                @$legajo->modalidad->privada
            ) : null,
            isset($legajo->rubro) ? new ObjectIdName(
                new ObjectId($legajo->rubro->id),
                new ObjectName(@$legajo->rubro->nombre),
            ) : null,
            $detalles
        );
    }

    private function getObjetoLegajoDetalle($detalle)
    {
        $tareas = null;
        if (isset($detalle->tareas))
        {
            foreach ($detalle->tareas as $t) {
                $tareas[] = new LegajoDetalleTarea(
                    new LegajoDetalleTareaId($t->id),
                    new LegajoDetalleTareaMonto(@$t->monto),
                    new TareaEmb(
                        new TareaId($t->tarea->id),
                        new TareaNombre(@$t->tarea->nombre),
                        new PorcentajeAporte(@$t->tarea->porcentaje_aporte),
                        @$t->tarea->cep_porcentaje_diferenciado
                    )
                );
            }
        }

        return new LegajoDetalleLegajoDetalle(
            new LegajoDetalleLegajoDetalleId($detalle->id),
            new LegajoDetalleFecha(new \DateTime($detalle->fecha)),
            new LegajoDetalleVencimiento(new \DateTime($detalle->vencimiento)),
            new LegajoDetalleMonto($detalle->monto),
            new LegajoLegajo(
                new LegajoLegajoId($detalle->legajo->id),
                new LegajoTipo($detalle->legajo->tipo),
                new LegajoNumeroLegajo($detalle->legajo->numero_legajo),
                isset($detalle->legajo->persona) ? new Persona(
                    new PersonaId(@$detalle->legajo->persona->id),
                    new PersonaNombre(@$detalle->legajo->persona->nombre),
                    new Documento(@$detalle->legajo->persona->documento),
                    new Apellido(@$detalle->legajo->persona->apellido),
                    new PersonaDistritoId(@$detalle->legajo->persona->distrito_id),
                    new Cuit(@$detalle->legajo->persona->cuit),
                ) : null,
                isset($detalle->legajo->matricula) ? new MatriculaMatricula(
                    new MatriculaMatriculaId($detalle->legajo->matricula->id),
                    new MatriculaNumeroMatricula(@$detalle->legajo->matricula->numero),
                    new MatriculaCodigoMatricula(@$detalle->legajo->matricula->codigo),
                    isset($detalle->legajo->matricula->titulo) ? new Titulo(
                        new TituloId($detalle->legajo->matricula->titulo->id),
                        new TituloDescripcion(@$detalle->legajo->matricula->titulo->descripcion),
                        new TituloCodigo(@$detalle->legajo->matricula->titulo->codigo),
                        isset($detalle->legajo->matricula->titulo->rubro) ? new ObjectIdName(
                            new ObjectId($detalle->legajo->matricula->titulo->rubro->id),
                            new ObjectName($detalle->legajo->matricula->titulo->rubro->nombre),
                        ) : null
                    ) : null,
                    isset($detalle->legajo->matricula->institucion) ? new ObjectIdName(
                        new ObjectId($detalle->legajo->matricula->institucion->id),
                        new ObjectName(@$detalle->legajo->matricula->institucion->nombre),
                    ) : null,
                    isset($detalle->legajo->matricula->categoria) ? new Categoria(
                        new CategoriaId($detalle->legajo->matricula->categoria->id),
                        new CategoriaNombre(@$detalle->legajo->matricula->categoria->nombre),
                        isset($detalle->legajo->matricula->categoria->tipo_categoria) ? new ObjectIdName(
                            new ObjectId($detalle->legajo->matricula->categoria->tipo_categoria->id),
                            new ObjectName(@$detalle->legajo->matricula->categoria->tipo_categoria->nombre),
                        ) : null
                    ) : null,
                ) : null,
                isset($detalle->legajo->distrito) ? new Distrito(
                    new DistritoDistritoId($detalle->legajo->distrito->id),
                    new NombreDistrito($detalle->legajo->distrito->nombre),
                    new Abreviatura($detalle->legajo->distrito->abreviatura),
                    isset($detalle->legajo->distrito->sede_cabecera) ? new SedeCabecera(
                        new SedeCabeceraId($detalle->legajo->distrito->sede_cabecera->id),
                        new NombreSede($detalle->legajo->distrito->sede_cabecera->nombre),
                        new SedeCabeceraAbreviatura($detalle->legajo->distrito->sede_cabecera->abreviatura),
                        new Contacto($detalle->legajo->distrito->sede_cabecera->contacto),
                        isset($detalle->legajo->distrito->sede_cabecera->partido) ? new ObjectIdName(
                            new ObjectId($detalle->legajo->distrito->sede_cabecera->partido->id),
                            new ObjectName(@$detalle->legajo->distrito->sede_cabecera->partido->nombre),
                        ) : null,
                        new SedeCabeceraCodigo($detalle->legajo->distrito->sede_cabecera->codigo),
                    ) : null
                ) : null,
                isset($detalle->legajo->modalidad) ? new Modalidad(
                    new ModalidadId(@$detalle->legajo->modalidad->id),
                    new NombreModalidad(@$detalle->legajo->modalidad->nombre),
                    new Sigla(@$detalle->legajo->modalidad->sigla),
                    @$detalle->legajo->modalidad->privada
                ) : null,
                isset($detalle->legajo->rubro) ? new ObjectIdName(
                    new ObjectId($detalle->legajo->rubro->id),
                    new ObjectName(@$detalle->legajo->rubro->nombre),
                ) : null,
            ),
            new LegajoDetalleTipoContrato($detalle->tipo_contrato),
            new LegajoDetalleMontoDiferenciado(@$detalle->monto_diferenciado),
            new LegajoDetalleNumeroContrato(isset($detalle->numero_contrato) ? intval($detalle->numero_contrato) : 0),
            $tareas
        );
    }

    private function getDataMatriculas($item){

        $matricula = $this->httpService->obtenerMatricula($item->getMatricula()->getValue());
        $titulo = $this->getTituloById($matricula->titulo_id);
        $categoria = $this->getCategoriaById($matricula->categoria_id);
        $planPago = $this->getPlanPagoById($item->getPlanPagoId()->getValue());

        return (object) [
            "matricula" => (object) [
                'id' => $matricula->id,
                'numero' => @$matricula->numero,
                'codigo' => @$matricula->codigo,
                'titulo' => (object) [
                    'id' => $titulo->id,
                    'descripcion' => @$titulo->descripcion,
                    'codigo' => @$titulo->codigo,
                    'rubro' => (object) [
                        'id' => $titulo->rubro,
                        'nombre' => @$titulo->rubro->nombre,
                    ]
                ],
                'categoria' => (object) [
                    'id' => $categoria->id,
                    'nombre' => @$categoria->nombre,
                    'tipo_categoria' => (object) [
                        'id' => $categoria->tipo_categoria->id,
                        'nombre' => @$categoria->tipo_categoria->nombre,
                    ]
                ],
            ],
            "cuota" => (object) [
                "id" => $item->getId(),
                "descripcion" => $item->getDescripcion()->getValue(),
                "concepto" => $item->getConcepto()->getKey()->getValue(),
                "tipo_item" => $item->getConcepto()->getTipoItem()->getKey()->getValue(),
                "numero" => $item->getNumero()->getValue(),
                "fecha_vencimiento" => $item->getVencimiento()->getValue()
            ],
            "plan_pago" => (object) [
                "id" => $planPago->id,
                "nombre" => $planPago->nombre,
                "prefijo" => $planPago->prefijo,
                "anio" => $planPago->anio
            ]
        ];
    }

    public function getTituloById($tituloId){
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-rel-matriculas/titulos/$tituloId",
                "GET"
            );

            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el titulo $tituloId");
        }    
    }

    public function getCategoriaById($categoriaId){
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-rel-matriculas/categorias/$categoriaId",
                "GET"
            );

            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener la categoria $categoriaId");
        }    
    }

    public function getPlanPagoById($planPagoId){
        try {
            $request = $this->apiConnection::httpRequest(
                "http://tec-api-cuotas/plan_pagos/$planPagoId",
                "GET"
            );
    
            return json_decode($request->getContent());
        } catch (\Throwable $th) {
            throw new BadRequestHttpException("Error al obtener el plan pago  $planPagoId");
        }    
    }
}