<?php

namespace App\Shared\Application\Service;

use DateTime;
use Exception;
use Throwable;
use Psr\Log\LoggerInterface;
use PhpParser\Node\Expr\Throw_;
use Symfony\Component\Uid\Uuid;
use Symfony\Component\Uid\UuidV4;
use App\Shared\Domain\Model\Sede\Sede;
use App\Shared\Domain\ValueObject\Modo;
use App\Shared\Domain\Model\Sede\Codigo;
use App\Shared\Domain\Model\Sede\SedeId;
use App\Shared\Domain\Model\Concepto\Key;
use App\Shared\Domain\Model\Persona\Cuit;
use App\Shared\Domain\Model\TipoItem\Tipo;
use App\Shared\Domain\ValueObject\Agentes;
use App\Shared\Domain\ValueObject\Reversa;
use App\Shared\Domain\Model\Concepto\Signo;
use App\Shared\Domain\Model\Persona\Nombre;
use App\Shared\Domain\Model\Persona\Persona;
use App\Shared\Domain\Model\Sede\NombreSede;
use App\Shared\Domain\ValueObject\Circuitos;
use App\Shared\Domain\Model\Persona\Apellido;
use App\Shared\Domain\Model\Sede\Abreviatura;
use App\Shared\Domain\Model\Concepto\Concepto;
use App\Shared\Domain\Model\Distrito\Distrito;
use App\Shared\Domain\Model\Persona\Documento;
use App\Shared\Domain\Model\Persona\PersonaId;
use App\Shared\Domain\Model\TipoItem\TipoItem;
use App\Shared\Domain\Model\GestionMonto\Valor;
use App\Shared\Domain\Model\Concepto\ConceptoId;
use App\Shared\Domain\Model\Distrito\DistritoId;
use App\Shared\Domain\Model\TipoItem\TipoItemId;
use App\Liquidacion\Domain\Model\Item\Referencia;
use App\Liquidacion\Domain\Model\Item\Descripcion;
use App\Shared\Domain\Model\Sede\Tipo as SedeTipo;
use App\Liquidacion\Domain\Model\Item\ImporteTotal;
use App\Liquidacion\Domain\Model\Item\Identificador;
use App\Shared\Domain\Model\Distrito\NombreDistrito;
use App\Shared\Domain\Model\GestionMonto\GestionMonto;
use App\Shared\Domain\Model\GestionMonto\Configuracion;
use App\Shared\Domain\Model\LiquidacionBase\MontoTotal;
use App\Shared\Domain\ValueObject\EstadosPosiblesCausa;
use App\Shared\Domain\Model\LiquidacionBase\OrdenPagoId;
use App\Shared\Domain\Model\LiquidacionBase\ValidoHasta;
use App\Shared\Domain\Model\TipoItem\Key as TipoItemKey;
use App\Liquidacion\Infrastructure\DTO\Input\Item\ItemDTO;
use App\Shared\Domain\Model\LiquidacionBase\LiquidacionId;
use App\Shared\Domain\ValueObject\IdentificadoresConceptos;
use App\Shared\Domain\Model\Concepto\Nombre as ConceptoNombre;
use App\Shared\Domain\Model\Concepto\Nombre as NombreConcepto;
use App\Shared\Domain\Model\TipoItem\Nombre as TipoItemNombre;
use App\Shared\Domain\Model\Persona\DistritoId as PersonaDistritoId;
use App\Shared\Domain\Repository\LiquidacionBaseRepositoryInterface;
use App\Liquidacion\Domain\Repository\LiquidacionRepositoryInterface;
use App\Shared\Domain\Model\Distrito\Abreviatura as DistritoAbreviatura;
use App\Shared\Domain\Model\LiquidacionBase\LiquidacionBase as Liquidacion;

date_default_timezone_set('America/Argentina/Buenos_Aires');
abstract class LiquidacionBaseApplicationService {

    const PORCENTAJE_GASTO_ADMINISTRATIVO = 20 ;
    protected $repository;
    protected $httpService;
    protected $logger;
    
    public function __construct(
        SharedHttpApplicationService $httpService,
        LiquidacionBaseRepositoryInterface $repository,
        LoggerInterface $logger,
    ){
        $this->httpService = $httpService;
        $this->repository = $repository;
        $this->logger = $logger;
    }

    public abstract function validarLiquidacion(Liquidacion $liquidacion): array ;

    public abstract function lanzarEventos(Liquidacion $liquidacion, ?string $reciboId = null, ?string $ordenPagoId = null) ;

    public abstract function lanzarEventosDesacritacion($recibo,$reversa);

    public abstract function validacionPuedeReversa($recibo);

    public abstract function calcularLiquidacion(array $items,$fecha_para_calcular = new DateTime('now'), array $args = []) ; 

    public abstract function validarItems($liquidacion,$orden_pago,&$nota_credito,&$monto_nota,$circuito);

    public abstract function validarDeuda($deuda,$items,$persona_id, array $args = []) ;

    public abstract function obtenerDeuda($items, $id,$fecha, array $args = []);

    public abstract function crearObjetoLiquidacion(LiquidacionId $liquidacionId, Persona $persona, ?string $distrito, ValidoHasta $validoHasta, MontoTotal $montoTotal, array $items, array $objetosAuxiliares, ?Sede $sede = null, ?string $createdBy);
    
    public abstract function crearArrayLiquidacion(array $persona_liquidacion, ?string $distrito, \DateTime $validoHasta, float $montoTotal, array $items, array $objetosAuxiliares, ?Sede $sede = null);

    public abstract function crearItemLiquidacion($item);

    public abstract function crearItemLiquidacionArray($item);

    public abstract function obtenerObjetoAuxiliar($item);

    //verificar si es necesario fecha en calculo para los distintos circuitos
    public function calculo($id,$items,$fecha = new DateTime('now'), array $args = []){

        $deuda = $this->obtenerDeuda($items, $id,$fecha, $args);

        $result_items['items'] = $this->calcularLiquidacion($deuda,clone($fecha),$args);

 	    $result_items['importe_total'] = $this->obtenerImporteTotalItems($result_items['items']) ;

        $result_items['fecha'] = $fecha ;

        return $result_items ;
    }


    /** metodo que permite generar cupon o recibo con sus respectivas validaciones 
     * 
     * @param $items items que componenen cupon/recibo
     * @param $pagos pagos para los casos de recibo manual
     * @param $modo tipo de accion "CUPON" O "RECIBO"
     * @param $flush est flag diferencia entre calculo y persisitir 
     * @param $id   identificador(persona_id,cep_id ...) dependiendo el circuito
     * 
    */
    public function generarCuponRecibo($items,$modo,$pagos,$persona_id,$notas_credito,$fechaCalculo,$preferencia_pagos,$circuito,$sede_id = null,$flush = true , $manual = false,$auditableUser = null){
        $monto_total = 0 ; 
        $vencimiento = null ;
        
        //ponemos esta condicion porque necesitamos que en el calcular, devuelva los elementos y calculos para que front pueda mostrar
        //las validaciones del calcular son mediante flag para front
        if($flush){
            $this->validacionesInicialesModo($items,$modo,$pagos,$persona_id);
        }

        $operacion_pago = $this->crearOperacionPago($pagos,$fechaCalculo);

        //solo para el caso de Cupon, calculamos si es feriado o fin de semana 
        if(empty($operacion_pago)){
            $fecha_calculo_manual = clone($fechaCalculo); //fecha que eligio en manual
            $vencimiento = $manual ? $fecha_calculo_manual : $this->getVencimiento($fechaCalculo->modify('last day of this month'));
            $fechaCalculo = clone($vencimiento);
        }
        //metodo que realiza calculos con los pagos y fechas
        $calculo = $this->obtenerCalculo($items, $notas_credito, $operacion_pago, $persona_id, $fechaCalculo, $sede_id);

        $monto_total += $calculo['importe_total'];

        //validacion post calculo (modo y importe)
        //comentamos estas validaciones porque las realiza con los flags
        //$this->validacionesPostCalculo($items,$calculo,$operacion_pago,$modo);

        //genero la liquidacion, sin persistir
 	    $liquidacion = $this->crearLiquidacion($calculo['items'], $persona_id, $sede_id,isset($vencimiento) ? $vencimiento : $fechaCalculo); 

        //genero ordenPago 
        $ordenPago = $this->crearOrdenPago($liquidacion, $notas_credito, $circuito, $preferencia_pagos,$auditableUser);

        $validacion = null;
        //si existe la operacion de pago , realizamos validaciones de pagos y orden 
        if(!empty($operacion_pago) || $modo == Modo::RECIBO ){

            //esta condicion es para los Recibos sin pagos , pero que le monto lo cubre una NC o Bonificacion manual
            if(empty($operacion_pago) & $monto_total <= 0 ){
                
                //genermos el pago con el instrumento acreditador, para simular un pago cuando se cubre el monto pero sin pagos
                $res = $this->generarAcreditacion($fechaCalculo);
                $operacion_pago = $res['operacion_pago'];
                $pagos = $res['pagos'];
            }

            $validacion = json_decode($this->httpService->validarOrden($operacion_pago, $ordenPago));

            $monto_total = $monto_total  - (array_key_exists('monto_total',$operacion_pago) ?  $operacion_pago['monto_total'] : 0);
        }

        if($flush) {
            //si no existe validacion, es Cupon, si existe es recibo y debe pasar la validacion
            if (!isset($validacion->valido) || $validacion->valido) {

                try{
                    //genero la liquidacion, sin persistir
                    $liquidacion = $this->generarLiquidacion($calculo['items'], $persona_id, $sede_id,$auditableUser); 
                    
                    $orden = isset($validacion) ? $validacion->orden_pago : $ordenPago ; 

                    $new_orden_pago = $this->generarOrdenPago($orden, $liquidacion); 
                    
                    //se comenta hasta que se suba agentes 
                    $this->actualizarPreferenciasPagos($new_orden_pago,$preferencia_pagos);

                    $liquidacion->setOrdenPagoId(new OrdenPagoId($new_orden_pago->id));
                    
                    $this->repository->persist($liquidacion);
                    //esto genera el recibo, la nota si corresponde y lanza los eventos 
                    if(!empty($operacion_pago)){

                        //metodo que se encarga de generar recibos y notas de credito si corresponde , lanzar eventos
                        $this->procesarPago($liquidacion,$new_orden_pago,$validacion->nota_credito,$validacion->monto_nota,$circuito,$validacion->valido);
                    }     
                
                    $this->repository->flush();
                    
                }catch (Throwable $th){
                    $this->logger->error(message: "Error al generar liquidacion/orden message: ".$th->__toString());
                    throw new Exception("Error al generar liquidacion/orden message: ");
                    
                }   

            } else {
                throw new Exception($validacion->message);  
            }

        }

        $result = [
            "liquidacion" => $liquidacion,
            "notas_credito" => $notas_credito,
            "pagos" => $pagos,
            "monto_total" => $monto_total,
            "fecha_calculo" => $fechaCalculo
        ]; 

        return $result ;
    }



    public function generarAcreditacion($fechaCalculo){

        $instrumento = $this->httpService->findInstrumentoByKey("acreditacion")[0];
        
        $pagos [] = (object)[
            "id" => null,
            "descripcion" => "Pago Acreditador",
            "instrumento" => (object)[
                "id"=> $instrumento->id,
                "nombre"=> $instrumento->nombre,
                "key"=> $instrumento->key,
                "tipo_item" => (object)[
                    "id"=> $instrumento->tipo_item->id,
                    "nombre"=> $instrumento->tipo_item->nombre,
                    "key"=> $instrumento->tipo_item->key,
                    "tipo"=> $instrumento->tipo_item->tipo
                ]
            ],
            "fecha_pago"=> ($fechaCalculo)->format('Y-m-d H:i:s'),
            "cuenta"=> null,
            "transaccion"=>  null,
            "operacion"=>  null,
            "importe_total" => 0
    
        ];

        $operacion_pago = $this->crearOperacionPago($pagos,$fechaCalculo);
        return [
            "operacion_pago" => $operacion_pago,
            "pagos" => $pagos,
        ];
    }

    public function crearLiquidacion($items_calculados, $persona_id, $sede_id = null,$fecha_calculo = new DateTime('now'))
    {
        $objetosAuxiliares =  [] ;
        $monto_total = 0 ;

        //items  
        $items = [];
        foreach ($items_calculados as $item) {

            
            $monto_total += ($item->importe_total * (isset($item->concepto->signo) ? $item->concepto->signo : 1) ) ;

            $items[] = $this->crearItemLiquidacionArray($item);

            $auxiliar = $this->obtenerObjetoAuxiliar($item);
            if ($auxiliar) $objetosAuxiliares[] = $auxiliar;             
        }

        //TODO hacer funcion absctracta para todos los circuitos para validar las liquidaciones

        
        //$monto_total = new MontoTotal($monto_total);

        //obtenemos la persona
        $persona = $this->httpService->obtenerPersonaOComitente($persona_id);
        
        $persona_liquidacion = [
            "personaId" => $persona->id,
            "nombre" => $persona->nombre,
            "documento" => $persona->documento,
            "cuit" => $persona->cuit,
            "codigo" => isset($persona->codigo) ? $persona->codigo : 0,
            "apellido" => $persona->apellido,
            "type" => $persona->type,
            "distrito" => isset($persona->distrito) ? $persona->distrito : null,
            "idSistemaAnterior" => isset($persona->id_sistema_anterior) ? $persona->id_sistema_anterior : null
        ];

        // TODO: Crear distrito, si va en todos los circuitos
        $distrito = $persona->distrito; // Puede ser null cuando no es un matriculado

        $sede = ($sede_id) ? $this->httpService->obtenerSede($sede_id) : null;
        if ($sede) {
            $sede = new Sede(
                new SedeId($sede->id),
                new NombreSede($sede->nombre),
                new Abreviatura($sede->abreviatura),
                new Distrito(
                    new DistritoId($sede->distrito->id),
                    new NombreDistrito($sede->distrito->nombre),
                    new DistritoAbreviatura($sede->distrito->abreviatura),
                ),
                new SedeTipo($sede->tipo),
                new Codigo($sede->codigo),
            );
        }

        if ($distrito == null) $distrito = $sede->getDistrito()->getId()->getValue();
        

        //liquidacion
        $liquidacion = $this->crearArrayLiquidacion($persona_liquidacion, $distrito, $fecha_calculo, $monto_total, $items, $objetosAuxiliares, $sede);

        return $liquidacion;
    }

    public function validacionesPostCalculo($items,$calculo,$operacion_pago,$modo){

        if($modo == modo::CUPON & ($calculo['importe_total'] <= 0 )){
            throw new Exception ("No se puede generar el cupon porque el importe es menor a 0");
        }

        if($modo == Modo::RECIBO){
            if ($operacion_pago['monto_total'] < $calculo['importe_total']){
                throw new Exception("Los pagos no cubren el monto total de los conceptos a pagar");
            }    
        }
        
        //ver para recibo 
    }
 
    public function obtenerCalculo($items, &$notas_credito, $operacion_pago, $persona_id, &$fechaCalculo, $sede_id = null)
    {
        $args = [
            'sedeId' => $sede_id
        ];

        $fecha_nota = null ;
        $importe_notas = $this->validarYcalcularNotas($notas_credito,$fecha_nota); 

        $result_bonificacion =  $this->calcularFechaBonificacion($items);
        $fecha_bonificacion = $result_bonificacion['fecha'];
        $importe_bonificacion = $result_bonificacion['importe'];

        // si hay pagos , debemos hacer el calculo retroactivo 
        if(!empty($operacion_pago) || $fecha_nota || $fecha_bonificacion) 
        {   
            $fecha_pago = isset($operacion_pago['fecha_pago']) ? new DateTime($operacion_pago['fecha_pago']) : null;

            //nos quedamos con la fecha mas grande
            $fecha_retroactiva = max(array_filter([$fecha_pago,$fecha_nota,$fecha_bonificacion]));

            $calculo = $this->calculo($persona_id, $items, $fecha_retroactiva, $args);

            $suma_pagos = isset($operacion_pago['monto_total']) ? $operacion_pago['monto_total'] + $importe_notas + $importe_bonificacion :  $importe_notas + $importe_bonificacion ;

            //si el monto total de los pagos alcanza a cubrir el calculo a la fecha , retornamos el calculo
            if($suma_pagos >= ($calculo['importe_total'] )){
                $fechaCalculo = $fecha_retroactiva;
                $calculo['importe_total'] = $calculo['importe_total'] - $importe_notas;
                return $calculo; 
            }else{
                $fechaCalculo = (new DateTime('now'));
                $calculo = $this->calculo($persona_id, $items, $fechaCalculo, $args);
                $calculo['importe_total'] = $calculo['importe_total'] - $importe_notas; 

                return $calculo;
            }
            //fecha calculo devolver
        }

        $fecha = clone $fechaCalculo;

        if (count($items) > 0) {
            $calculo = $this->calculo($persona_id, $items, $fecha, $args);
        } else {
            $calculo['items'] = [];
            $calculo['importe_total'] = 0;
            $calculo['fecha'] = $fechaCalculo;
        }

        $calculo['importe_total'] = $calculo['importe_total'] - $importe_notas; 

        return $calculo;
    }

    private function calcularFechaBonificacion($items){

        $fecha_bonificacion = null;
        $importe_bonificacion = 0 ;
        foreach($items as $item){
            if($item->concepto->tipo_item->key == IdentificadoresConceptos::TIPO_BONIFICACION && $item->concepto->key != IdentificadoresConceptos::BONIFICACION_ANUAL && $item->concepto->key != IdentificadoresConceptos::BONIFICACION_MULTIPLE){
                $importe_bonificacion =+ $item->importe_total;
                if($fecha_bonificacion){
                    $fecha_bonificacion = $fecha_bonificacion < $items->fecha ?? $item->fecha ;
                }else{
                    $fecha_bonificacion = $item->fecha ;
                }
            }
        }

        return [
            "fecha" => $fecha_bonificacion,
            "importe" => $importe_bonificacion
        ];
    }

    private function validarYcalcularNotas(&$notas_credito,&$fecha_nota)
    {
        $importes_notas = 0 ;
        foreach($notas_credito as &$nc){
            $nota = $this->httpService->obtenerNotaCreditoById($nc['id']);

            if(isset($nota->destino)) throw new Exception("Nota de crédito ya utilizada.");

            $nc['fecha'] = new \DateTime($nota->fecha_recibo);
            $nc['descripcion'] = "NOTA DE CREDITO #".$nota->numero;
            $nc['importe'] = $nota->monto_total;

            $importes_notas += $nota->monto_total ;

            if($fecha_nota){
                $fecha_nota = $fecha_nota < $nc['fecha'] ?? $nc['fecha'];
            }else{
                $fecha_nota = $nc['fecha'];
            }
        }

        return $importes_notas ;
    }

    public function fechaMasRecientePagos($pagos){
        $fecha_max_pago = null ;

        foreach($pagos as $pago){
            
            if(is_null($fecha_max_pago)){
                $fecha_max_pago = $pago->fechaPago; 
            }

            if($pago->fechaPago > $fecha_max_pago ){
                $fecha_max_pago = $pago->fechaPago ;
            }
        }

        return $fecha_max_pago ;
    }

    /**
     * Metodo que realiza validaciones correspondientes , de acuerdo al modo
     */
    public function validacionesInicialesModo($items, $modo, $pagos, $id = null)
    {
        if($modo == Modo::CUPON) {
            if(count($pagos) > 0){
                throw new Exception ("Para el modo cupon no se pueden tener pagos");
            }
        }

        
    }


     /** Metodo que permite procesar y validar un pago mediante una operacion 
     *  - se valida la orden de pago y se asocia la operacion 
     *  - si la validacion de recaudacion fue exitosa , liquidacion realiza sus validaciones
     *  - si las validaciones fueron correctas se crea un recibo y una NC si corresponde
     *  - se marca la orden de paga como paga y se actualiza el campo reciboId para asociar la orden al recibo 
    */
    public function validarOperacionPago($operacionPago,$ordenPago,$liquidacion,$username = null) {

        //flags de control
        $monto_pagos = 0 ;
        $monto_nota = 0 ;
        $message = "" ;

        //validamos la operacion + la orden en el MS de recaudacion
        $validacion = json_decode($this->httpService->validarOrden($operacionPago,$ordenPago));
       
        $this->logger->info("Validacion exitosa: " .$validacion->valido ."Mensaje: ".$validacion->message);

        //TODO mejorar esto con la respuesta de recaudacion haciendo un DTO
        $new_orden_pago = $validacion->orden_pago;
        $new_orden_pago->operaciones_pagos[0]->id = $operacionPago['id'] ;
        $new_orden_pago->id = $operacionPago['orden_pago_id'];
        $circuito = $new_orden_pago->circuito ;

        //metodo que se encarga de generar recibos y notas de credito si corresponde , lanzar eventos
        $result = $this->procesarPago($liquidacion,$new_orden_pago,$validacion->nota_credito,$validacion->monto_nota,$circuito,$validacion->valido,$operacionPago['recibo_id'],$username);
        $itemsValidos = $result['items_validos'];
        $recibo = $result['recibo'];

        if(!$validacion->valido){
            $this->actualizarOrdenOperacionPago($operacionPago,$recibo, $new_orden_pago->id);
        }


        $nc = $this->calculoFlagsNotaCredito($validacion,$result);

        //ver aca que hacemos
        $result = [
            'valido'  => $validacion->valido,
            'message' => $validacion->message,
            'orden_pago' => $ordenPago,
            'nota_credito' => $nc['nota_credito'],
            'monto_nota' => $nc['monto_nota'],
            'reciboId' => $recibo->id,
            'numeroRecibo' => $recibo->numero,
            'items_validos' => $itemsValidos
        ];

        return $result ;

    }

    public function calculoFlagsNotaCredito($validacion, $result){
        
        $genero_nota_credito = false;
        $monto_nota = 0 ;
        //esta validacion es por si se genero una NC por la validacion de items y no por la validacion general de la orden 
        if($validacion->nota_credito){
            $genero_nota_credito = true;
            $monto_nota = $validacion->monto_nota;
        }else{
            if(!$result['items_validos']){
                $genero_nota_credito = true;
                $monto_nota = $result['monto_nota'];
            }
        }

        return [
            "nota_credito" => $genero_nota_credito,
            "monto_nota" => $monto_nota
        ];
    }

    public function actualizarOrdenOperacionPago($op,$recibo,$orden_id){

        $op['recibo_id'] = $recibo->id ;
        
        $body = $this->generarBodyOperacionPago($op);

        $this->httpService->actualizarOrden($orden_id,$body);

    }

    public function generarBodyOperacionPago($op){

        $items = [];

        $operacion = [];
        foreach($op['items'] as $item){
            $items[] = [
                "id" => isset($item->id) ? $item->id : UuidV4::v4()->__toString(),
                "descripcion" => $item->descripcion,
                "instrumento" => [
                    "id" => $item->instrumento->id,
                    "nombre" => $item->instrumento->nombre,
                    "key" => $item->instrumento->key,
                    "signo" => $item->instrumento->signo,
                    "tipo_item" => [
                        "id" => $item->instrumento->tipo_item->id,
                        "nombre" => $item->instrumento->tipo_item->nombre,
                        "key" => $item->instrumento->tipo_item->key,
                        "tipo" => $item->instrumento->tipo_item->tipo,
                    ]
                ],
                "fecha_pago" => $item->fecha_pago,
                "cuenta" => $item->cuenta,
                "transaccion" => $item->transaccion,
                "operacion" => $item->operacion,
                "importe_total" => $item->importe_total
            ];
        }

        $operacion = [
            "id" => $op['id'],
            "microservicio" => $op['microservicio'],
            "agente" => $op['agente'],
            "monto_total" => $op['monto_total'],
            "items" => $items ,
            "fecha_pago" => $op['fecha_pago'],
            "fecha" =>  $op['fecha'],
            "orden_pago_id" => $op['orden_pago_id'],
            "recaudador_id" => $op['recaudador_id'],
            "recibo_id" => $op['recibo_id']
        ];
        
        $body = [
            "operaciones_pagos" => [$operacion] 
        ];

        return $body ;
    }

    public function procesarPago($liquidacion, $ordenPago, $nota_credito, $monto_nota, $circuito, $valido,$reciboId = null,$username=null)
    {
        $this->logger->info("ProcesarPago...");

        $itemsValidos = true ;
        //es valido cuando paso las validaciones de recaudacion y de items
        if($valido){
            //validacion de items 
            $itemsValidos = $this->validarItems($liquidacion,$ordenPago,$nota_credito,$monto_nota,$circuito);

            //reciboId existe cuando se esta identificando un pago que se habia generado sin identificar
            if(!$reciboId){
                //generar recibo y nota si corresponde
                $this->logger->info("Generando recibo y nota si corresponde");
                
                //para el caso de que los items no sean validos debemos generar el recibo y la nota de credito, ya que no se puede acreditar el cupon
                if(!$itemsValidos){
                    $this->logger->info("Los items no pasaron la validacion, se generara una nota de credito por el monto total");
                    $nota_credito = true ;
                    $monto_nota = isset($ordenPago->operaciones_pagos[0]->monto_total) ? $ordenPago->operaciones_pagos[0]->monto_total : $ordenPago->operaciones_pagos[0]->montoTotal;
                }
                
                $recibo = $this->generarReciboNota($liquidacion,$ordenPago,$nota_credito,$monto_nota,$circuito,$itemsValidos);

            }else{
                $this->logger->info("Se actualiza el Recibo ".$reciboId." por ser un identificacion de proceso");
                $recibo = $this->actualizarReciboNoIdentificado($reciboId,$ordenPago,$liquidacion,$nota_credito,$monto_nota,$circuito);
            }

            if($itemsValidos){
                //actualizamos la orden a isPaga = true 
                $this->logger->info("Actualizando orden de pago");

                $ope = json_decode(json_encode($ordenPago->operaciones_pagos[0]), true); 
                $ope['recibo_id'] = $recibo->id ;
                $ope['orden_pago_id'] = $ordenPago->id;
                $ope['recaudador_id'] = null ;
                
                $ordenPago = $this->httpService->actualizarOrden($ordenPago->id,["is_paga" => true,"recibo_id" => $recibo->id , "fecha_pago" => $recibo->pagos[0]->fecha_pago, "operaciones_pagos" => [$ope],"updated_by" => $username]) ;
                
                //si la orden contiene notas de creditos, y el pago fue exitoso, actualizamos el destino.
                if(count($ordenPago->items) > 0) {
                    $this->logger->info("Actualizando notas de credito");
                    $this->actualizarNotasCredito($ordenPago->items, $recibo->id);
                }
                
                $this->logger->info("Lanzando eventos. Recibo: ".@$recibo->id.". OrdenPago: ".@$ordenPago->id);
                $this->lanzarEventos($liquidacion, $recibo->id, $ordenPago->id);
                    
            }else{
                $this->logger->info("No se lanzan eventos. Items no validados. OrdenPago: ".@$ordenPago->id);
            }
        }else{  
                $this->logger->info("Generando recibo y nota si corresponde");
                $recibo = $this->generarReciboNota(null,$ordenPago,$nota_credito,$monto_nota,$circuito);    
        }
        return [
            "recibo" => $recibo,
            "items_validos" => $itemsValidos,
            "nota_credito" => $nota_credito,
            "monto_nota" => $monto_nota 
        ];
    }

    public function generarReciboNota($liquidacion, $ordenPago, $nota_credito, $monto_nota, $circuito,$items_validos = null ,$username = null)
    {    
        
        $matriculas  = [] ;

        $items = $this->generarItemsRecibo($liquidacion, $ordenPago, $nota_credito, $monto_nota, $circuito,$items_validos);
       $pagos = [] ;
       $importe_total = 0 ;
      //TODO temporal, corregir como devuelve la info recaudacion , mediante DTO ()
       foreach($ordenPago->operaciones_pagos as $op ){
            $items_pago = [] ;
            $importe_total = isset($op->monto_total) ? $op->monto_total : $op->montoTotal ;
            foreach($op->items as $item){
                $fecha_pago = new DateTime(isset($item->fecha_pago) ? $item->fecha_pago : $item->fechaPago) ;
                $items_pago[] = [
                    "descripcion"=> $item->descripcion,
                    "instrumento"=> [
                        "id"=> $item->instrumento->id,
                        "nombre"=> $item->instrumento->nombre,
                        "key"=> $item->instrumento->key,
                        "signo" => $item->instrumento->signo,
                        "tipo_item"=> [
                            "id"=> isset($item->instrumento->tipo_item) ? $item->instrumento->tipo_item->id : $item->instrumento->tipoItem->id,
                            "nombre"=> isset($item->instrumento->tipo_item) ? $item->instrumento->tipo_item->nombre : $item->instrumento->tipoItem->nombre ,
                            "key"=> isset($item->instrumento->tipo_item) ? $item->instrumento->tipo_item->key : $item->instrumento->tipoItem->key,
                            "tipo"=> isset($item->instrumento->tipo_item) ? $item->instrumento->tipo_item->tipo : $item->instrumento->tipoItem->tipo 
                        ]
                    ],
                    "fecha_pago"=> $fecha_pago->format('Y-m-d H:i:s'),
                    "cuenta"=> isset($item->cuenta) ? $item->cuenta : null,
                    "transaccion"=> isset($item->transaccion) ? $item->transaccion : null ,
                    "operacion"=> isset($item->operacion) ? $item->operacion : null,
                    "importe_total" => isset($item->importe_total) ? $item->importe_total : $item->importeTotal 
                ];
            }

            $pagoFecha = new DateTime(isset($op->fecha_pago) ? $op->fecha_pago : $op->fechaPago);
            $fecha = new DateTime($op->fecha);
            $pagos[] = [
                 "microservicio"=> $op->microservicio,
                 "agente"=> $op->agente,
                 "fecha"=> $fecha->format('Y-m-d H:i:s'),
                 "importe_total"=> $importe_total,
                 "items"=> $items_pago,
                 "orden_pago_id"=> isset($ordenPago->id) ? $ordenPago->id : $op->orden_pago_id,
                 "recaudador_id"=> isset($op->recaudador_id) ? $op->recaudador_id : null ,
                 "fecha_pago"=> $pagoFecha->format('Y-m-d H:i:s')
            ];
       }
       
        $distrito = $this->httpService->findDistritoById($ordenPago->distrito);

        $sede = null ;
        if($liquidacion){
            $sede = ($liquidacion->getSede()) ? [
                "id" => $liquidacion->getSede()->getId()->getValue(),
                "nombre" => $liquidacion->getSede()->getNombre()->getValue(),
                "abreviatura" => $liquidacion->getSede()->getAbreviatura()->getValue(),
            ] : null;
        }

        $body = [
            "persona" => [
                "id" => $ordenPago->persona->id,
                "nombre"=> $ordenPago->persona->nombre,
                "apellido"=> $ordenPago->persona->apellido,
                "documento"=> $ordenPago->persona->documento,
                "distritoId"=> @$ordenPago->persona->distritoId
            ],
            "matriculas"=> $matriculas,
            "distrito"=> [
                "id" => $distrito->id,
                "nombre" => $distrito->nombre 
            ],
            "sede" => $sede,
            "tipo"=> "recibo",
            "items"=> $items,
            "pagos"=> $pagos ,
            "nota_credito_id"=> null,
            "orden_pago"=> isset($ordenPago->id) ? $ordenPago->id : $op->orden_pago_id,
            "numero_boleta" => $ordenPago->numero_boleta,
            "monto_total"=> $importe_total,
            "fecha_calculo"=> (new DateTime("now"))->format('Y-m-d H:i:s'),
            "numero"=> null ,
            "fecha_recibo"=> (new DateTime("now"))->format('Y-m-d H:i:s'),
            "nota_credito"=> $nota_credito,
            "monto_credito"=> $monto_nota,
            "circuito" => $circuito,
            "operacion_pago_id" => $ordenPago->operaciones_pagos[0]->id,
            "created_at" => new DateTime("NOW"),
            "created_by" => $username = null ? $username : $ordenPago->created_by 
        ];

        $reciboNota = $this->httpService->newReciboNota($body);

        return $reciboNota;

    }

    public function generarItemsRecibo($liquidacion,$ordenPago,$nota_credito, $monto_nota, $circuito,$items_validos = null){
        
        $items = [] ;

        if($items_validos){
            if($liquidacion){
                foreach($liquidacion->getItems() as $item){
                    $items [] = [
                        "id"=> $item->getId(),
                        "descripcion"=> $item->getDescripcion()?$item->getDescripcion()->getValue():null,
                        "concepto"=> [
                            "id"=> $item->getConcepto()->getId(),
                            "nombre"=> $item->getConcepto()->getNombre()->getValue(),
                            "key"=> $item->getConcepto()->getKey()->getValue(),
                            "signo" => $item->getConcepto()->getSigno()->getValue(),
                            "gestion_monto"=> [
                                "configuracion"=> $item->getConcepto()->getGestionMonto()->getConfiguracion()->getValue(),
                                "valor"=> $item->getConcepto()->getGestionMonto()->getValor() ? $item->getConcepto()->getGestionMonto()->getValor()->getValue() : null,
                            ],
                            "tipo_item"=> [
                                "id"=> $item->getConcepto()->getTipoItem()->getId(),
                                "nombre"=> $item->getConcepto()->getTipoItem()->getNombre()->getValue(),
                                "key"=> $item->getConcepto()->getTipoItem()->getKey()->getValue(),
                                "tipo"=> $item->getConcepto()->getTipoItem()->getTipo()->getValue()
                            ]
                        ] ,
                        "identificador"=> $item->getIdentificador()?$item->getIdentificador()->getValue():null,
                        "referencia"=> $item->getReferencia()?$item->getReferencia()->getValue():null,
                        "importe_total"=> $item->getImporteTotal()->getValue(),
                        "metadata"=> method_exists($item,"getMetadata") ? $item->getMetadata() : null ,
                    ];
                }

                // TODO: Es para todos los circuitos?
                $matriculas = null;
                if (method_exists($liquidacion, 'getMatriculas'))
                {
                    foreach($liquidacion->getMatriculas() as $mat){
                        $matriculas[] = [
                            "id"=> $mat->getId()->getValue(),
                            "codigo"=> $mat->getCodigo()->getValue(),
                            "titulo_id"=> $mat->getTituloId()->getValue()
                        ];
                    }
                }            
            }

            //se agregan las notas de credito , si se utilizo alguna para el pago
            if(count($ordenPago->items) > 0){
                
                $concepto = $this->httpService->findConceptoByKey(IdentificadoresConceptos::NOTA_CREDITO)[0];
                
                foreach($ordenPago->items as $item){
                    $items [] = [
                        "id"=> $item->id,
                        "descripcion"=> $item->descripcion,
                        //"matricula"=> $item->getMatricula()->getValue(), // Bastaria con identificador
                        "concepto"=> [
                            "id"=> $concepto->id,
                            "nombre"=> $concepto->nombre,
                            "key"=> $concepto->key,
                            "signo" => $concepto->signo ,
                            "gestion_monto"=> [
                                "configuracion"=> $concepto->gestion_monto->configuracion,
                                "valor"=> isset($concepto->gestion_monto->valor)? $concepto->gestion_monto->valor :null,
                            ],
                            "tipo_item"=> [
                                "id"=> $concepto->tipo_item->id,
                                "nombre"=> $concepto->tipo_item->nombre,
                                "key"=> $concepto->tipo_item->key,
                                "tipo"=> $concepto->tipo_item->tipo
                            ]
                        ],
                        "identificador"=> null,
                        "referencia"=> null,
                        "importe_total"=> $item->importe_total,
                        "metadata"=>null ,
                    ];
                }
            }

            //si corresponde nota de credito , generamos un item de debito
            if($nota_credito){ 
                $items [] = $this->generarItemDebito($monto_nota);
            }

        }else{
            $monto_total = isset($ordenPago->operaciones_pagos[0]->monto_total) ? $ordenPago->operaciones_pagos[0]->monto_total : $ordenPago->operaciones_pagos[0]->montoTotal;
            //generamos el elemento debito para los casos de items no validos 
            $items [] = $this->generarItemDebito($monto_total);
        }    

        return $items;
    }

    /**
     * Metodo que permite generar un items debito para recibos/NC
     */
    private function generarItemDebito($importeTotal){
        //remplazar por constante
        $concepto = $this->httpService->findConceptoByKey(IdentificadoresConceptos::NOTA_DEBITO)[0];

        return [
            "id" => Uuid::v4()->__toString(),
            "descripcion"=> "Debito",
            "concepto"=> [
                "id"=> $concepto->id,
                "nombre"=> $concepto->nombre,
                "key"=> $concepto->key,
                "signo" => $concepto->signo ,
                "gestion_monto"=> [
                    "configuracion"=> $concepto->gestion_monto->configuracion,
                    "valor"=> isset($concepto->gestion_monto->valor)? $concepto->gestion_monto->valor :null,
                ],
                "tipo_item"=> [
                    "id"=> $concepto->tipo_item->id,
                    "nombre"=> $concepto->tipo_item->nombre,
                    "key"=> $concepto->tipo_item->key,
                    "tipo"=> $concepto->tipo_item->tipo
                ]
            ] ,
            "importe_total"=> $importeTotal //monto del debito = al valor de la nota de credito
        ];

    }


    public function actualizarReciboNoIdentificado($reciboId,$orden,$liquidacion,$nota_credito,$monto_nota,$circuito,$username = null){

        $distrito = $this->httpService->findDistritoById($orden->distrito);

        $persona = [
            'id' => $orden->persona->id,
            'nombre' => $orden->persona->nombre,
            'apellido' => $orden->persona->apellido,
            'documento' => $orden->persona->documento
        ];

       $items = $this->generarItemsRecibo($liquidacion, $orden, $nota_credito, $monto_nota, $circuito);

        $body = [
            'persona' => $persona,
            'orden_pago' => $orden->id,
            'numero_boleta' => $orden->numero_boleta,
            'operacion_pago_id' => $orden->operaciones_pagos[0]->id,
            'distrito' => [
                'id' => $distrito->id,
                'nombre' => $distrito->nombre
            ],
            'items' => $items,
            "updated_by" => $username ? $username : null ,
            
        ];
        $recibo = $this->httpService->updateRecibo($body,$reciboId); 

        return $recibo ;
    }

    /** metodo que permite generar una operacion de pago a partir de pagos  */
    public function crearOperacionPago(&$pagos,$fechaCalculo){

        $items = [] ;
        $total_operacion = 0;
        $fecha_max_pago = null ;
        $op = [] ;
        
        foreach($pagos  as $pago){
            $total_operacion += $pago->importe_total;
             
            if(is_null($fecha_max_pago)){
                $fecha_max_pago = $pago->fecha_pago; 
            }

            if($pago->fecha_pago > $fecha_max_pago ){
                $fecha_max_pago = $pago->fecha_pago ;
            }

            $instrumento = $this->createInstrumentoArray($pago->instrumento);
            $pago->instrumento = $instrumento;

            $cuenta = isset($pago->cuenta) ? $this->createCuentaArray($pago->cuenta) : null;
            $pago->cuenta = $cuenta;

            //foreach($pago->items as $it){
            $items[] = [
                "id" => $pago->id,
                "descripcion" => isset($pago->descripcion) ? $pago->descripcion : $instrumento['nombre'],
                "instrumento" => $instrumento,
                "fecha_pago"=> $pago->fecha_pago,
                "cuenta"=>  ($cuenta) ? $cuenta['id'] : null,
                "transaccion"=>  $pago->transaccion,
                "operacion"=>  $pago->operacion,
                "importe_total" => $pago->importe_total
        
            ];
            //}    

            $op = [
                "microservicio" => "LIQUIDACION",
                "agente" => Agentes::AGENTE_CAJA_DISTRITAL,
                "monto_total"=> $total_operacion,
                "items" => $items ,
                "fecha_pago"=> $fecha_max_pago,//($fecha_max_pago)->format('Y-m-d H:i:s'), //analizar en este caso que fecha iria
                "fecha"=>  ($fechaCalculo)->format('Y-m-d H:i:s'),
                "orden_pago_id"=> null,
                "recaudador_id"=> null 
            ];
    
        }

        return $op ;
    }


    /**
     *  Metodo que genera una liquidacion a partir de items pasados 
     */
    public function generarLiquidacion($items_calculados, $persona_id, $sede_id,$auditableUser = null)
    {
        $objetosAuxiliares =  [] ;
        $monto_total = 0 ;
        $fecha_valido = null ;

        //items  
        $items = [];
        foreach ($items_calculados as $item) {

            // TODO: esto es para todos los circuitos? idem crearLiquidacion()
            //se realiza esta validacion para en el caso de bonificacion, tomar la fecha de vencimiento de la misma para el vencimiento de la liquidacion
            if(isset($item->concepto->tipo_key) && $item->concepto->tipo_key == IdentificadoresConceptos::TIPO_BONIFICACION){
                $fecha_valido = $item->fecha_vencimiento;
            }
            
            $monto_total += $item->importe_total * (isset($item->concepto->signo)?$item->concepto->signo:1) ;

             //identificar y limpiar el id para los temporales
            if(isset($item->id) && strpos($item->id, 'T-') === 0){
                $idt = $item->id; 
                $item->id = str_replace('T-', '', $idt);
            }
            
            $items[] = $this->crearItemLiquidacion($item);

            $auxiliar = $this->obtenerObjetoAuxiliar($item);
            if ($auxiliar) $objetosAuxiliares[] = $auxiliar; //item->matricula ;
        }

        //generamos el id para la liquidacion
        $liquidacionId = new LiquidacionId(Uuid::v4()->__toString()); 
        
        $monto_total = new MontoTotal(round($monto_total,2));

        //obtenemos la persona
        $persona = $this->httpService->obtenerPersonaOComitente($persona_id);
        
        //persona
        $persona_liquidacion = new Persona(
            new PersonaId($persona->id),
            new Nombre($persona->nombre),
            new Documento($persona->documento),
            new Apellido($persona->apellido),
            new PersonaDistritoId($persona->distrito),
            new Cuit(isset($persona->cuit) ?  $persona->cuit : null),
            $persona->type
        );
        $persona_liquidacion->setIdSistemaAnterior(isset($persona->id_sistema_anterior) ? $persona->id_sistema_anterior : null );

        $distrito = $persona->distrito;

        $sede = ($sede_id) ? $this->httpService->obtenerSede($sede_id) : null;
        if ($sede) {
            $sede = new Sede(
                new SedeId($sede->id),
                new NombreSede($sede->nombre),
                new Abreviatura($sede->abreviatura),
                new Distrito(
                    new DistritoId($sede->distrito->id),
                    new NombreDistrito($sede->distrito->nombre),
                    new DistritoAbreviatura($sede->distrito->abreviatura),
                ),
                new SedeTipo($sede->tipo),
                new Codigo($sede->codigo),
            );
        }

        if ($distrito == null) $distrito = $sede->getDistrito()->getId()->getValue();
        
        //sino hay una fecha_valida definida, sumamos 1 mes a la fecha de hoy 
        if (!$fecha_valido) {
            $fecha_valido = new DateTime();
            $fecha_valido->modify('+1 month');
        } else {
            $fecha_valido = new DateTime($fecha_valido);
        }
        $fecha_valido = new ValidoHasta($fecha_valido);
        
        //liquidacion
        $liquidacion = $this->crearObjetoLiquidacion($liquidacionId, $persona_liquidacion, $distrito, $fecha_valido, $monto_total, $items, $objetosAuxiliares, $sede,$auditableUser->username);

        return $liquidacion;
    }

    public function createTipoConceptoArray($c){

        $concepto = $this->httpService->findConceptoByKey($c->key)[0];    

        $concep = [
            'id' => $concepto->id,
            'nombre' =>$concepto->nombre,
            'key' => $concepto->key,
            'signo' => $concepto->signo,
            'gestion_monto' => [
                'configuracion' => $concepto->gestion_monto->configuracion,
                'valor' => isset($concepto->gestion_monto->valor) ? $concepto->gestion_monto->valor : 0,
            ],
            'tipo_item' => [
                'id' => $concepto->tipo_item->id,
                'nombre' => $concepto->tipo_item->nombre,
                'key' => $concepto->tipo_item->key,
                'tipo' => $concepto->tipo_item->tipo
            ]
        ];
        
        return $concep ;
    }

    public function createCuentaArray($cuenta)
    {
        $c = $this->httpService->findCuentaById($cuenta->id);    

        $arr = [
            'id' => $c->id,
            'nombre' =>$c->nombre,
            'numero' => $c->numero,
            'sucursal' => $c->sucursal,
            'banco' => [
                'id' => $c->banco->id,
                'nombre' => $c->banco->nombre
            ]
        ];
        
        return $arr;
    }

    public function createInstrumentoArray($i){

        $instrumento = $this->httpService->findInstrumentoByKey($i->key)[0];    

        $inst = [
            'id' => $instrumento->id,
            'nombre' =>$instrumento->nombre,
            'key' => $instrumento->key,
            'signo' => $instrumento->signo,
            'tipo_item' => [
                'id' => $instrumento->tipo_item->id,
                'nombre' => $instrumento->tipo_item->nombre,
                'key' => $instrumento->tipo_item->key,
                'tipo' => $instrumento->tipo_item->tipo
            ]
        ];
        
        return $inst;
    }

    public function createTipoConcepto($c){

        $concepto = $this->httpService->findConceptoByKey($c->key)[0];    

        $concep = new Concepto(
            new ConceptoId($concepto->id),
            new ConceptoNombre($concepto->nombre),
            new Key($concepto->key),
            new Signo($concepto->signo),
            new GestionMonto(
                new Configuracion($concepto->gestion_monto->configuracion),
                new Valor(isset($concepto->gestion_monto->valor) ? $concepto->gestion_monto->valor : 0)),
            new TipoItem(
                new TipoItemId($concepto->tipo_item->id),
                new TipoItemNombre($concepto->tipo_item->nombre),
                new TipoItemKey($concepto->tipo_item->key),
                new Tipo($concepto->tipo_item->tipo)
            )
        );
        
        return $concep ;
    }

    /*
    public function crearItemLiquidacionBonficacion($item){
        
        $concepto = $this->httpService->obtenerConcepto($item['concepto_id']);

        $it =  array(
            'id' => new ItemId(Uuid::v4()->__toString()),
            'descripcion' => new Descripcion($item['descripcion']),
            'matricula' => new MatriculaId(isset($item['matricula_id']) ? $item['matricula_id'] : null),
            'identificador' => new Identificador(null),
            'concepto' => $this->createTipoConcepto($concepto),
            'importeOriginal' => new ImporteOriginal($item['importe']),
            'ajuste' => new Ajuste(0),
            'interes' => new Interes(0),
            'vencimiento' => new Vencimiento($item['fecha_vencimiento'] instanceof DateTime ? $item['fecha_vencimiento'] : DateTime::createFromFormat('Y-m-d',substr($item['fecha_vencimiento'],0,10)) ),
            'importeTotal' => new ImporteTotal($item['importe']),
        );

        return $it ;
    }


    public function crearItemLiquidacionSancion(ItemDTO $item){
        
        $concepto = $this->httpService->findConceptoByKey($item->getConcepto()->getKey())[0];
        
        $causa = $this->httpService->findCausaById($item->getId()) ;

        $it =  array(
            'id' => new ItemId(Uuid::v4()->__toString()),
            'descripcion' => new Descripcion($item->getDescripcion()),
            'matricula' => new MatriculaId($item->getMatricula()->getId()),
            'identificador' => new Identificador($item->getId()), //en el caso de las sanciones en el id del item almacenamos el id de causa, por eso lo seteamos aca
            'concepto' => $this->createTipoConcepto($concepto),
            'importeOriginal' => new ImporteOriginal($item->getImporteOriginal()),
            'ajuste' => new Ajuste(0),
            'interes' => new Interes(0),
            'vencimiento' => new Vencimiento($item->getVencimiento()),
            'importeTotal' => new ImporteTotal($item->getImporteTotal()),
            'idSistemaAnterior' => $causa->id_sistema_anterior 
        );

        return $it ; 
    }
    */


    public function crearOrdenPago($liquidacion, $notas_creditos, $circuito, $pref_pagos,$audtiableUser=null)
    {   
        $matriculas = [] ;
        foreach($liquidacion['matriculas'] as $matricula){
            $matriculas [] = [
                'id' => $matricula['id'], // FIX: Ver si se puede enviar solo el ID y que lo embeba la orden de pago en su MS
                'codigo' => $matricula['codigo'],
                'titulo_id' => $matricula['tituloId'],
                'id_sistema_anterior' => $matricula['idSistemaAnterior']
            ];
        }

        $itemsLiquidacion = $liquidacion['items'];
        
        $liquidaciones[] = [
            'items' => $itemsLiquidacion ,
            'monto_total' => $liquidacion['montoTotal'],
            'valido_hasta' => $liquidacion['validoHasta'] ? $liquidacion['validoHasta']->format('Y-m-d H:i:s') : (new DateTime())->modify('last day of this month')->format('Y-m-d H:i:s')
        ];
      
        $preferencia_pagos = [];
        // TODO: Implementar esta parte
        foreach($pref_pagos as $pref)
        {
            if($pref->key == "codigo_barra")
            {
                $preferencia_pagos[] = [
                    'key' => $pref->key,
                    'nombre' => $pref->nombre,
                    'monto' => $pref->monto,
                    'agentes' => "banco provincia",
                    'codigo_barra' => "",
                    'visible' => true ,
                    'seleccionada' => true ,
                    'microservicio' => "" 
                ];
            }
        }

        $items_nota = [];
        $monto_notas = 0 ;
        foreach($notas_creditos as $nota){
            $monto_notas += $nota['importe'];
            $items_nota [] = [
                "id" => $nota['id'],
                "fecha" => $nota['fecha']->format('Y-m-d H:i:s'),
                "descripcion" => $nota['descripcion'],
                "importe_total" => $nota['importe']
            ];
        }

        $sede = @$liquidacion['sede'];

        $ordenPago = [
            'persona' => [
                'id' => $liquidacion['persona']['personaId'],
                'nombre' => $liquidacion['persona']['nombre'],
                'apellido' => $liquidacion['persona']['apellido'],
                'documento' => $liquidacion['persona']['documento'],
                'codigo' => $liquidacion['persona']['codigo'],
                'distritoId' => @$liquidacion['persona']['distritoId'],
                'id_sistema_anterior' => @$liquidacion['persona']['idSistemaAnterior']
            ],
            'matriculas' => $matriculas, // **
            'distrito' => @$liquidacion['distrito'], // *
            'sede' => ($sede) ? [
                'id' => $sede->getId()->getValue(),
                'nombre' => $sede->getNombre()->getValue(),
                'abreviatura' => $sede->getAbreviatura()->getValue(),
            ] : null, // *
            // TODO: aca para 'vencimiento' se puede hacer una funcion template, que segun el circuito se defina un vencimiento en la ORDEN especifica
            'vencimiento' => $liquidacion['validoHasta'] ? $liquidacion['validoHasta']->format('Y-m-d H:i:s') : (new DateTime())->modify('last day of this month')->format('Y-m-d H:i:s'), 
            'monto_total' => $liquidacion['montoTotal'] - $monto_notas,
            'liquidaciones' => $liquidaciones,
            'preferencias_pagos' => $preferencia_pagos ,
            'operaciones_pagos' => [],
            'items' => $items_nota,
            'recibo_id' => null ,
            'is_paga' => false ,
            'fecha_creacion' => null ,
            'numero_boleta' => null,
            'id_sistema_anterior' => null,
            'circuito' => $circuito,
            'created_at' => new DateTime("NOW"),
            'created_by' => $audtiableUser->username
        ];

        return $ordenPago ;
    }

    public function generarOrdenPago($ordenPago,$liquidacion = null){

        //asignamos el id de liquidacion 
        //ver como mejorarlos, se hace esta condicion porque cuando tiene pagos , va al servicio de recaudacion y vuelve en otro formato
        if(is_array($ordenPago)){
            $ordenPago['liquidaciones'][0]['id'] = $liquidacion->getId()->getValue();
        }else{
            $ordenPago->liquidaciones[0]->id = $liquidacion->getId()->getValue() ;
        }

        return $this->httpService->newOrdenPago($ordenPago);
    }

    
    /**
    * Metodo que obtiene las preferencias de pago posibles para la liquidacion
    * (primera version hardcodeado el codigo_barra_v1, luego hacer refactor  para obtener estos datos de un enpoint )
    */
    public function obtenerPreferenciasDePagos($monto) {

        //en una segunda version , modificar por enpoint con todas las opciones, en primera version solo codigo_barra_v1
        $preferencias_pagos[] = (object)[
            'key' => 'codigo_barra',
            'nombre' => 'codigo de barras',
            'monto' => $monto
        ];
        
        return $preferencias_pagos ;
    }

    /**
    * Metodo que si corresponde calcula la sanciones de un matriculado, consultando las causas por la matricula 
    * @param $matricula matricula persona
    * @param $plan  plan vigente 
        */
    public function obtenerSancion($matricula,$plan_vigente){
            
        $sanciones = [] ;
        
        $causas = $this->httpService->findCausaByMatricula($matricula->id);
        
        foreach($causas as $causa){
            //corresponde sancion sila causa esta Firme
            if($causa->estado_actual_id == EstadosPosiblesCausa::FIRME['id']){
                
                //si periodo_multa y modulos_multa es 0 , no es una sancion economica
                if($causa->sancion->periodos_multa > 0 || $causa->sancion->modulos_multa > 0 ){
                    
                    if ($causa->sancion != null && !$causa->is_paga ){
                        $sanciones [] = $this->generarSancion($causa,$matricula,$plan_vigente) ;
                    }    
                }
            }
        }
        
        return $sanciones;
        
    }

     /**
    * Genera la sancion con los calculos correspondientes
    * @param $causa causas de una matricula 
    * @param $matricula matricula persona
    * @param $plan plan vigente
    * 
    * @return ItemDTO
    */
   public function generarSancion($causa,$matricula,$plan){
     
        //calculamos el importe de la sancion
        //multiplicamos la cantidad de peridos de multa , por el valor del semestre del plan vigente
        //multiplicamos la cantidad de modulos de multa por el valor del arancel de reinscripcion (que es equivalente al valor_modulo del sistema anterior)
        $importe = 0 ;
        $importe += ($plan->valor_anual * ($causa->sancion->periodos_multa/2));
        $importe += ($plan->valor_modulo * $causa->sancion->modulos_multa) ;

        $concepto = $this->httpService->findConceptoByKey(IdentificadoresConceptos::MULTA)[0];

        $item = (object)[
            "id" => $causa->id,
            "identificador" => $causa->id,
            "matricula" => (object)[ 
                "id" => $matricula->id,
                "numero" => @$matricula->numero,
                "codigo" => @$matricula->codigo,
                "titulo_id" => $matricula->titulo_id,
                "institucion_id" => $matricula->institucion_id,
                "categoria_id" => $matricula->categoria_id,
                "persona_id" => $matricula->persona->id
            ],
            "descripcion" => "SANCION CAUSA ".$causa->numero,
            "concepto" => (object) [
                'key' => $concepto->key,
                'tipo_item' => (object)[
                    "key" => $concepto->tipo_item->key 
                ],
                'signo' => $concepto->signo
            ],
            "plan_pago" => $plan,
            "numero" => 0,
            "importe" => round($importe,0),
            "ajuste" => 0 ,
            "interes" => 0 ,
            "fecha_vencimiento" => (new DateTime('last day of December this year'))->format('Y-m-d H:i:s'),
            "importe_total" => round($importe,0)
        ];

        return $item ;
    }
    
    /**
     * ver de aplicar signos a los conceptos 
     */
    public function obtenerImporteTotalItems($items){
        
        $importe_total = 0 ;

        foreach($items as $item){
           $importe_total += $this->getImporteItem($item);
        }

        return $importe_total ;
    }

    /**
     * metodo que devuelve el importe de un item con signo (+ -)
    */ 
    public function getImporteItem($item){
        $signo = property_exists($item->concepto, 'signo') ? $item->concepto->signo : 1;
        $importe_item = $item->importe_total * $signo  ;
       
        return $importe_item;
    }

    public function contieneCreditos($items){
        $credito = [] ;
        foreach($items as $item){
            if($item->concepto->key == IdentificadoresConceptos::NOTA_CREDITO){
                $credito[] = $item ;
            }
        }
        return $credito ;
    }


    public function actualizarNotasCredito($items,$recibo_destino){

        foreach($items as $item ){
            $body = [
                "destino" => $recibo_destino
            ];

            $this->httpService->updateNotaCredito($body,$item->id);
        }
    }


    //realiza el calculo para devolver el vencimiento en un dia habil
    public function getVencimiento($vencimiento = null){
        
        //obtenemos todos los feriados del año 
        $feriados_anio = $this->httpService->obtenerFeriadoByAnio($vencimiento->format('Y'));

        if ($vencimiento == null){
            $vencimiento = new DateTime('NOW');
            $vencimiento->modify('last day of this month');
        }
        
        //se ejecuta hasta que sea un dia habil
        $flag = false;
        while(!$flag){
            if($this->isFinDeSemana($vencimiento) || $this->isFeriado($vencimiento->format('Y-m-d'),$feriados_anio)){
                $vencimiento->modify('-1 day');
            }else{
                $flag = true ;
            }

        }

        return $vencimiento ;

    }
    
    /**
     * Chequea si un dia en particular es sabado o domingo
     */
    public function isFinDeSemana($date){
        $diaSemana = (int)$date->format('w');
        return ($diaSemana == 6 || $diaSemana == 0);
    }
    
    /**
     * Chequea si un dia en particular es feriado
     */
    public function isFeriado($date,$feriados){
        
        foreach($feriados as $feriado){
            if($date == (substr($feriado->fecha,0,10)) ){
                return true ;
            }
        }

        return false ;
        
    }

    public function generarCodigoBarra($ordenPago){

        $version = "1" ;
        if($ordenPago->circuito == 'matriculas'){
            $version = $ordenPago->monto_total < 100000 ? "1" : "3" ;
        }

        // TODO: cambiar a snake case cuando se configure en agentes
        $body = [
            "cuponId" => (string) $ordenPago->numero_boleta,
            "vencimiento" => $ordenPago->vencimiento,
            "importe" => $ordenPago->monto_total >= 0 ? (string) $ordenPago->monto_total : "0",
            "personaId" => (string) $ordenPago->persona->codigo,
            "documento" => (string) $ordenPago->persona->documento,
            "fechaCupon" => $ordenPago->fecha_creacion ,
            "circuito" => $ordenPago->circuito,
            "version" => $version ,
            "ordenId" => $ordenPago->id ,
            "personaUid" => $ordenPago->persona->id
        ];

        $codigo_barra = $this->httpService->obtenerCodigoBarra($body);

        return $codigo_barra ;
    }

    public function actualizarPreferenciasPagos($ordenPago,$preferencia_pagos){
        $codigo_barra = $this->generarCodigoBarra($ordenPago);
        
        $preferencias = [] ;
        if(empty($preferencia_pagos)){
            //pref para todos los circuitos
            $preferencias[] = (object) [
                "key" => "codigo_barra",
                "nombre" => "Codigo Barra",
                "monto" => $ordenPago->monto_total,
                "agentes" => "", //TODO mejora completar con los agentes, hoy front no los utiliza
                "codigo_barra" => $codigo_barra->codigoBarra, // TODO: cambiar a codigo_barra cuando se implemente snake case
                "visible" => true,
                "seleccionada" => true,
                "microservicio" => $ordenPago->circuito
            ];

            if($ordenPago->circuito == Circuitos::TIMBRADOS){
                
                $preferencias[] = (object) [
                    "key" => "online",
                    "nombre" => "Pago online",
                    "monto" => $ordenPago->monto_total,
                    "agentes" => "", //TODO mejora completar con los agentes, hoy front no los utiliza
                    "codigo_barra" => $codigo_barra->codigoBarra, // TODO: cambiar a codigo_barra cuando se implemente snake case
                    "visible" => true,
                    "seleccionada" => true,
                    "microservicio" => $ordenPago->circuito
                ];
            }else{
                //para el caso CEP o Matriculas
                $preferencias[] = (object) [
                    "key" => "redLink",
                    "nombre" => "redLink",
                    "monto" => $ordenPago->monto_total,
                    "agentes" => "", //TODO mejora completar con los agentes, hoy front no los utiliza
                    "codigo_barra" => $codigo_barra->codigoBarra, // TODO: cambiar a codigo_barra cuando se implemente snake case
                    "visible" => true,
                    "seleccionada" => true,
                    "microservicio" => $ordenPago->circuito
                ];
                
                if($ordenPago->circuito  == Circuitos::MATRICULADOS){
                    
                    $preferencias[] = (object) [
                        "key" => "sipago",
                        "nombre" => "SiPago",
                        "monto" => $ordenPago->monto_total,
                        "agentes" => "", //TODO mejora completar con los agentes, hoy front no los utiliza
                        "codigo_barra" => $codigo_barra->codigoBarra, // TODO: cambiar a codigo_barra cuando se implemente snake case
                        "visible" => true,
                        "seleccionada" => true,
                        "microservicio" => $ordenPago->circuito
                    ];
                }
            }

        }else{
            
            foreach($preferencia_pagos as $p){
                $preferencias [] = [
                    "key" => $p->key,
                    "nombre" => $p->nombre,
                    "monto" => $p->monto,
                    "agentes" => "", //TODO mejora completar con los agentes, hoy front no los utiliza
                    "codigo_barra" => $codigo_barra->codigoBarra, // TODO: cambiar a codigo_barra cuando se implemente snake case
                    "visible" => true,
                    "seleccionada" => true,
                    "microservicio" => $ordenPago->circuito
                ];
            }
        }
        
        $body = [            
            "preferencias_pagos" => $preferencias    
        ];

        $this->httpService->actualizarOrden($ordenPago->id,$body);

    }

    public function generarReversaRecibo($recibo,$instrumento,$modo,$porcentajeGastoAdm,$porcentajeModulo,$audtiableUser= null)
    {   
        if(!$this->esReciboDebito($recibo)){
            if (!$this->validacionPuedeReversa($recibo)) throw new Exception("El recibo no cumple las condiciones para poder generar ".$modo);
        }

        $items = $recibo->items ;
        $pagos = [] ;

        //generamos los items de la reversa dependiendo el modo
        $res = $this->generarItemsReversa($items,$modo,$porcentajeGastoAdm,$porcentajeModulo,$this->esReciboDebito($recibo),$recibo->id,$recibo);

        $items = $res['items'];

        $total_items = $res['total'];
        
        if($total_items > 0){
            throw new Exception("El recibo no cumple las condiciones para poder generar ".$modo);
        }

        //Generamos los items de pago de la reversa
        $pagos = $this->generarPagoReversa($instrumento[0],$total_items,$recibo,$modo) ;

        //generamos el recibo Reversa
        $reciboReversa = $this->generarReversa($recibo,$items,$pagos,$total_items,$audtiableUser);

        //si es una desacreditacion , generamos una NC para que pueda ser usado el pago del recibo desacreditado
        if($modo == Reversa::MODO_DESACREDITACION){
            //solo generamos una nota si hay salgo a devolver del pago, y en caso que se utilizo una NC se libera para poder volver a usar
            if($recibo->pagos[0]->importe_total > 0){
                $nc = $this->generarNotaCredito($recibo,$reciboReversa,$audtiableUser);
            }
        }   
        
        //si se utilizo una NC para el pago del recibo, al hacer el reverso , liberamos nuevamente la NC
        $this->liberarNotaCredito($recibo);
        
        //si el recibo a reversar , habia generar una nota de credito , le setteamos el destino el nuevo reverso
        if($reciboReversa && isset($recibo->nota_credito_id)){
            
            $notaCredito = $this->httpService->obtenerNotaCreditoById($recibo->nota_credito_id);

            $this->httpService->updateNotaCredito(["destino" => $reciboReversa->id,"update_at" => new DateTime("NOW"),"update_by" => $audtiableUser->username], $notaCredito->id);
        }

        //lanzamos los eventos de desacreditacion y actualizamos la orden
        if(!$this->esReciboDebito($recibo)){
            $this->lanzarEventosDesacritacion($recibo,$reciboReversa); 

            $this->httpService->actualizarOrden($reciboReversa->orden_pago,['reversa' => true,"update_at" => new DateTime("NOW"),"update_by" => $audtiableUser->username]);
        }

        return $reciboReversa ;
    }


    private function generarReversa($recibo,$items,$pagos,$total_items,$auditableUser= null){
        
        $body = [
            "idRecibo" => $recibo->id,
            "persona" => [
                "id" => $recibo->persona->id,
                "nombre"=> $recibo->persona->nombre,
                "apellido"=> $recibo->persona->apellido,
                "documento"=> $recibo->persona->documento,
                "distritoId"=> @$recibo->persona->distritoId
            ],
            "matriculas"=> $recibo->matriculas,
            "distrito"=> [
                "id" => $recibo->distrito->id,
                "nombre" => $recibo->distrito->nombre 
            ],
            "sede" => isset($recibo->sede) ? $recibo->sede : null,
            "tipo"=> "reversa",
            "items"=> $items,
            "pagos"=> $pagos ,
            "nota_credito_id"=> null,
            "orden_pago"=> $recibo->orden_pago,
            "numero_boleta" => $recibo->numero_boleta,
            "monto_total"=> $total_items < 0 ? round($total_items,2) : 0,
            "fecha_calculo"=> (new DateTime("now"))->format('Y-m-d H:i:s'),
            "numero"=> null ,
            "fecha_recibo"=> (new DateTime("now"))->format('Y-m-d H:i:s'),
            "circuito" => $recibo->circuito,
            "created_at" => new DateTime("NOW"),
            "created_by" => @$auditableUser->username
        ];

        $reciboReversa = $this->httpService->newReciboReversa($body);

        return $reciboReversa ;
    }

    /**
     * Permite en caso que en el recibo se utilizo una nota de credito como parte del pago, poder liberarla al hacer el reverso
     * @param mixed $recibo
     * @return void
     */
    public function liberarNotaCredito($recibo){
        foreach($recibo->items as $item){
            if($item->concepto->key == IdentificadoresConceptos::NOTA_CREDITO){
                $notaCredito = $this->httpService->obtenerNotaCreditoById($item->id);
                $this->httpService->desacreditarNotaCredito($notaCredito->id);
            }
        }
    }

    public function generarNotaCredito($recibo,$reversa,$auditableUser= null){
        
        $concepto = $this->httpService->findConceptoByKey(IdentificadoresConceptos::NOTA_CREDITO)[0];

        $matriculas = [];

        foreach($recibo->matriculas as $mat ){
            $matriculas[] = [
                "id" => $mat->id,
                "codigo" => $mat->codigo,
                "tituloId" => $mat->titulo_id,
            ];
        }

        $pagos = [];
        $items_pagos = [];
        foreach($recibo->pagos as $pago){
            
            foreach($pago->items as $i ){
                $items_pagos[] =  [
                    "descripcion"=> $i->descripcion,
                    "instrumento"=> [
                        "id"=> $i->instrumento->id,
                        "nombre"=> $i->instrumento->nombre,
                        "key"=> $i->instrumento->key,
                        "signo" => $i->instrumento->signo ?? 1,
                        "tipo_item"=> [
                            "id"=>  $i->instrumento->tipo_item->id ,
                            "nombre"=>  $i->instrumento->tipo_item->nombre, 
                            "key"=>  $i->instrumento->tipo_item->key, 
                            "tipo"=>  $i->instrumento->tipo_item->tipo, 
                        ],
                    ],
                    "fecha_pago"=> $i->fecha_pago,
                    "cuenta"=> @$i->cuenta,
                    "transaccion"=> @$i->transaccion ,
                    "operacion"=> @$i->operacion,
                    "importe_total" => $i->importe_total
                ];
            }

            $pagos[] = [
                "microservicio"=> $pago->microservicio,
                "agente"=> $pago->agente,
                "fecha"=> $pago->fecha,
                "importe_total"=> $pago->importe_total,
                "items" => $items_pagos,
                "orden_pago_id" => @$pago->orden_pago_id,
                "recaudador_id"=> @$pago->recaudador_id ,
                "fecha_pago"=> $pago->fecha_pago,
            ];
        }

        $body = [
            "origen" => $reversa->id,
            "destino" => null,
            "persona" => [
                "id" => $recibo->persona->id,
                "nombre"=> $recibo->persona->nombre,
                "apellido"=> $recibo->persona->apellido,
                "documento"=> $recibo->persona->documento,
                "distritoId"=> @$recibo->persona->distritoId
            ],
            "matriculas"=> $matriculas,
            "distrito"=> [
                "id" => $recibo->distrito->id,
                "nombre" => $recibo->distrito->nombre 
            ],
            "items" => [
                [
                    "id" =>null,
                    "descripcion" => $concepto->nombre,
                    "concepto" => [
                        "id" => $concepto->id,
                        "nombre" => $concepto->nombre,
                        "key" => $concepto->key,
                        "signo" => $concepto->signo,
                        "gestion_monto" => [
                            "configuracion" => $concepto->gestion_monto->configuracion,
                            "valor" => isset($concepto->gestion_monto->valor) ? $concepto->gestion_monto->valor : null
                        ],
                        "tipo_item" => [
                            "id" => $concepto->tipo_item->id,
                            "nombre" => $concepto->tipo_item->nombre,
                            "key" => $concepto->tipo_item->key,
                            "tipo" => $concepto->tipo_item->tipo 
                        ]

                    ],
                    "identificador" => $reversa->id,
                    "importeTotal" => $recibo->monto_total,
                    "referencia" => $recibo->id,
                    "metadata" => null
                ]
            ],
            "pagos" => $pagos,
            "orden_pago" => $reversa->orden_pago,
            "numero_boleta" => $recibo->numero_boleta,
            "monto_total"  => $recibo->monto_total,
            "nota_credito_id" => null,
            "fecha_calculo"=> $reversa->fecha_calculo,
            "numero"=> null ,
            "fecha_recibo"=> $reversa->fecha_recibo,
            "circuito" => $reversa->circuito,
            "created_at" => new DateTime("NOW"),
            "created_by" => @$auditableUser->username
        ];

        $nota_credito = $this->httpService->generarNotaCredito($body);

        return $nota_credito;
    }

    public function esReciboDebito($recibo){

        if(count($recibo->items) == 1 && $recibo->items[0]->concepto->key == IdentificadoresConceptos::NOTA_DEBITO){
            return true;
        }else{
            return false ;
        }
    }

    private function getItemReversa($concepto,$item = null,$importeTotal = null,$signoInvertido = true){

        $items_rev = [
            "id" => Uuid::v4()->__toString(),
            "descripcion" => $this->obtenerDescripcionItem($concepto,$item),
            "matricula" => isset($item->identificador) ? $item->identificador : null,
            "metadata" => @$item->metadata,
            "concepto" => [
                "id" => $concepto->id,
                "nombre" => $concepto->nombre,
                "key" => $concepto->key,
                "signo" => $concepto->signo,
                "gestion_monto" => [
                    "configuracion" => $concepto->gestion_monto->configuracion,
                    "valor" => isset($concepto->gestion_monto->valor) ? $concepto->gestion_monto->valor : null
                ],
                "tipo_item" => [
                    "id" => $concepto->tipo_item->id,
                    "nombre" => $concepto->tipo_item->nombre,
                    "key" => $concepto->tipo_item->key,
                    "tipo" => $concepto->tipo_item->tipo 
                ]

            ],
            "identificador" => isset($item->identificador) ? $item->identificador : null,
            "importeTotal" => isset($item->importe_total) ? round($item->importe_total,2) : round($importeTotal,2),
            "referencia" => isset($item->referencia) ? $item->referencia : null,
            "signoInvertido" => $signoInvertido
        ];

        return $items_rev; 
    }
    
    /**
     * Devuelve la descripcion para el items , dependiendo el concepto 
     * @param mixed $concepto
     * @param mixed $item
     * @return string
     */
    private function obtenerDescripcionItem($concepto,$item = null){

        switch($concepto->key){
            case IdentificadoresConceptos::GASTO_ADMINISTRATIVO:
                $descripcion = "Gasto Administrativos Reintegro" ;
                break;
            case IdentificadoresConceptos::NOTA_DEBITO:
                $descripcion = "Debito";
                break;
            default:
                $descripcion = "Reversa ".$item->descripcion;
        }

        return $descripcion ;
    }


    public function generarItemsReversa($items,$modo,$porcentajeGastoAdm,$porcentajeModulo,$reciboDebito,$reciboId,$recibo){
        
        $total = 0 ;
        $suma_items = 0;
        $items_rev = [];
        //por cada items generamos el equivalente para la reversa
        foreach($items as $item){
            $items_rev[] = $this->getItemReversa($item->concepto,$item);
        }
        
        //si es reintegro, debemos generar un gasto administrativo
        if($modo == Reversa::MODO_REINTEGRO){
            //calculamos el gasto administrativo y generamos el item asociado
            $items_rev[] = $this->calculoGastoAdministrativo($recibo,$item,$porcentajeGastoAdm,$porcentajeModulo); 
        }
        
        //si es una desacreditacion , debemos agregar un debito con importe igual a la suma de items a desacreditar, para que los pagos sean 0
        if($modo == Reversa::MODO_DESACREDITACION) {
            //generamos el items del debito
            $items_rev[] = $this->calcularItemDebito($items_rev) ;
        }

        //obtenemos el total de los items de reversa
        $total = $this->obtenerTotalItems($items_rev);
        
        return [
            'items' => $items_rev,
            'total' => $total 
        ];
    }


    private function calcularItemDebito($items){
        
        $concepto = $this->httpService->findConceptoByKey(IdentificadoresConceptos::NOTA_DEBITO)[0];
            
        $monto_debito = abs($this->obtenerTotalItems($items));

        $item_debito = $this->getItemReversa($concepto,null, $monto_debito ,false);
        $item_debito['importeTotal'] = $monto_debito ;

        return $item_debito ;

    }

    private function calculoGastoAdministrativo($recibo,$item,$porcentajeGastoAdm,$porcentajeModulo){
        
        $plan = $this->httpService->obtenerPlanVigente(new DateTime()) ;

        //aplicamos un porcentaje fijo para el gasto administrativo
        $gastoAdm = $recibo->pagos[0]->importe_total * $porcentajeGastoAdm / 100 ;
        
        //aplicamos un porcenjate fijo para el valor del modulo
        $gastoModulo = ($plan->valor_modulo * $porcentajeModulo) / 100 ;

        $gastoTotal = $gastoAdm + $gastoModulo ;
        
        $concepto = $this->httpService->findConceptoByKey(IdentificadoresConceptos::GASTO_ADMINISTRATIVO)[0];

        $item_gasto = $this->getItemReversa($concepto,$item,null,false);

        //asignamos el importe del gasto administrativo
        $item_gasto['importeTotal'] = $gastoTotal;

        return $item_gasto ;
    }

    public function obtenerTotalItems($items){

        $total = 0 ;
        foreach($items as $item){
            //si tiene el flag signo invertido multiplicamos por sus signo y luego por (-1)
            $total += $item['signoInvertido']  ? $item['importeTotal'] * $item['concepto']['signo'] * (-1) : $item['importeTotal'] * $item['concepto']['signo'] ;
        }

        return $total;
    }

    public function generarPagoReversa($instrumento,$importe,$recibo,$modo){

        $items_pago = [];
        $items_pago [] = [
            "descripcion"=> "Pago Reversa",
            "instrumento"=> [
                "id"=> $instrumento->id,
                "nombre"=> $instrumento->nombre,
                "key"=> $instrumento->key,
                "signo" => $instrumento->signo,
                "tipo_item"=> [
                    "id"=>  $instrumento->tipo_item->id ,
                    "nombre"=>  $instrumento->tipo_item->nombre, 
                    "key"=>  $instrumento->tipo_item->key, 
                    "tipo"=>  $instrumento->tipo_item->tipo, 
                ],
            ],
            "fecha_pago"=> (new DateTime())->format('Y-m-d H:i:s'),
            "cuenta"=> null,
            "transaccion"=> null ,
            "operacion"=> null,
            "importe_total" => round(abs($importe),2)
        ];


        $pagos[] = [
            "id" => Uuid::v4()->__toString(),
            "microservicio" => "Liquidacion",
            "agente" => $modo == Reversa::MODO_ANULACION ? $recibo->pagos[0]->agente: Agentes::AGENTE_CAJA_DISTRITAL,
            "fecha" => (new DateTime())->format('Y-m-d H:i:s'),
            "importeTotal" => round(abs($importe),2) ,
            "items" => $items_pago,
            "ordenPagoId" => null,
            "recaudadorId" => null,
            "fechaPago" => (new DateTime())->format('Y-m-d H:i:s')
        ];

        return $pagos ;
    }


    /**
     *  Metodo que permite procesar una operacion sin identificar , generando un recibo 
     * @param mixed $op
     * @return mixed
     */
    public function procesarOperacionSinIdentificar($op){

        $items_pago = [] ;

        foreach($op['items'] as $item){
            $fecha_pago = new DateTime(isset($item->fecha_pago) ? $item->fecha_pago : $item->fechaPago) ;
            $items_pago[] = [
                "descripcion"=> $item->descripcion,
                "instrumento"=> [
                    "id"=> $item->instrumento->id,
                    "nombre"=> $item->instrumento->nombre,
                    "key"=> $item->instrumento->key,
                    "signo" => $item->instrumento->signo,
                    "tipo_item"=> [
                        "id"=> isset($item->instrumento->tipo_item) ? $item->instrumento->tipo_item->id : $item->instrumento->tipoItem->id,
                        "nombre"=> isset($item->instrumento->tipo_item) ? $item->instrumento->tipo_item->nombre : $item->instrumento->tipoItem->nombre ,
                        "key"=> isset($item->instrumento->tipo_item) ? $item->instrumento->tipo_item->key : $item->instrumento->tipoItem->key,
                        "tipo"=> isset($item->instrumento->tipo_item) ? $item->instrumento->tipo_item->tipo : $item->instrumento->tipoItem->tipo 
                    ]
                ],
                "fecha_pago"=> $fecha_pago->format('Y-m-d H:i:s'),
                "cuenta"=> isset($item->cuenta) ? $item->cuenta : null,
                "transaccion"=> isset($item->transaccion) ? $item->transaccion : null ,
                "operacion"=> isset($item->operacion) ? $item->operacion : null,
                "importe_total" => isset($item->importe_total) ? $item->importe_total : $item->importeTotal 
            ];
        }

        $pagoFecha = new DateTime($op['fecha_pago']);
        $fecha = new DateTime($op['fecha']);
        $pagos[] = [
             "microservicio"=> $op['microservicio'],
             "agente"=> $op['agente'],
             "fecha"=> $fecha->format('Y-m-d H:i:s'),
             "importe_total"=>$op['monto_total'],
             "items"=> $items_pago,
             "orden_pago_id"=> null,
             "recaudador_id"=> $op['recaudador_id'],
             "fecha_pago"=> $pagoFecha->format('Y-m-d H:i:s')
        ];

        //remplazar por constante
        $concepto = $this->httpService->findConceptoByKey(IdentificadoresConceptos::NOTA_DEBITO)[0];

        $item_debito [] = [
            "descripcion"=> "Debito",
            "concepto"=> [
                "id"=> $concepto->id,
                "nombre"=> $concepto->nombre,
                "key"=> $concepto->key,
                "signo" => $concepto->signo ,
                "gestion_monto"=> [
                    "configuracion"=> $concepto->gestion_monto->configuracion,
                    "valor"=> isset($concepto->gestion_monto->valor)? $concepto->gestion_monto->valor :null,
                ],
                "tipo_item"=> [
                    "id"=> $concepto->tipo_item->id,
                    "nombre"=> $concepto->tipo_item->nombre,
                    "key"=> $concepto->tipo_item->key,
                    "tipo"=> $concepto->tipo_item->tipo
                ]
            ] ,
            "importe_total"=> $op['monto_total']
        ];

        
        $body = [
            "persona" => [
                "id" => "-1",
                "nombre"=> "DESCONOCIDO",
                "apellido"=> "SIN IDENTIFICAR",
                "documento"=> 99999999999,
                "distritoId"=> null
            ],
            "matriculas"=> [],
            "distrito"=> null,
            "sede" => null,
            "tipo"=> "recibo",
            "items"=> $item_debito,
            "pagos"=> $pagos ,
            "nota_credito_id"=> null,
            "orden_pago"=> null,
            "numero_boleta" => null,
            "monto_total"=>$op['monto_total'],
            "fecha_calculo"=> (new DateTime("now"))->format('Y-m-d H:i:s'),
            "numero"=> null ,
            "fecha_recibo"=> (new DateTime("now"))->format('Y-m-d H:i:s'),
            "nota_credito"=> false,
            "monto_credito"=> 0,
            "circuito" => $op['microservicio'],
            "created_by" => "sistema"
        ];

        return $this->httpService->newReciboNota($body);

    }
}