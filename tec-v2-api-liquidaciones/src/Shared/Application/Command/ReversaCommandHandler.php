<?php

namespace App\Shared\Application\Command;

use App\Shared\Domain\ValueObject\Reversa;
use App\Shared\Domain\ValueObject\IdentificadoresInstrumentos;
use App\Shared\Application\Service\SharedHttpApplicationService;
use App\Liquidacion\Application\Command\ContextGenericCommandHandler;
use App\Liquidacion\Application\Service\LiquidacionApplicationService as ServiceMat;
use App\LiquidacionCep\Application\Service\LiquidacionApplicationService as ServiceCep;
use App\LiquidacionTimbrado\Application\Service\LiquidacionApplicationService as ServiceTim;
use App\Shared\Domain\ValueObject\Circuitos;
use Exception;

class ReversaCommandHandler extends ContextGenericCommandHandler{

    public function __construct(
        private ServiceCep $liqCepService,
        private ServiceMat $liqMatService,
        private ServiceTim $liqTimService,
        private SharedHttpApplicationService $httpService
    ){}

    public function __invoke(ReversaCommand $command){

        $modo = $command->modo ;

        $porcentaje = $command->porcentaje ;

        $porcentajeModulo = $command->procentajeModulo;

        if($modo == Reversa::MODO_ANULACION){
            $key_instrumento = IdentificadoresInstrumentos::ANULACION ;
        }else{
            $key_instrumento = IdentificadoresInstrumentos::REINTEGRO;
        }

        $instrumento = $this->httpService->findInstrumentoByKey($key_instrumento);
        
        $recibo = $this->httpService->obtenerReciboById($command->reciboId);

        if(isset($recibo->nota_credito_id)){
            
            $notaCredito = $this->httpService->obtenerNotaCreditoById($recibo->nota_credito_id);

            if(isset($notaCredito->destino)){
                throw new Exception("No se puede Reversar el recibo ".$command->reciboId.", porque tiene asociada una notaCredito que ya fue utilizada ");
            }
        }

        if(isset($recibo->reversa_id)){
            throw new Exception("No se puede Reversar el recibo ".$command->reciboId.", ya se le aplico un reverso ");
        }
        
        $service = null ;

        switch($recibo->circuito){
            case Circuitos::MATRICULADOS:
                $service = $this->liqMatService ;
                break;
            case Circuitos::CEP:
                $service = $this->liqCepService ;
                break;
            case Circuitos::TIMBRADOS:
                $service = $this->liqTimService ;
                break;
        }

        $reversa = $service->generarReversaRecibo($recibo,$instrumento,$modo,$porcentaje,$porcentajeModulo,$command->getAuditableUser());

        return $reversa;
    }
}
