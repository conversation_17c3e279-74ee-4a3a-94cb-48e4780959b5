<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mongo-mapping 
    xmlns="http://doctrine-project.org/schemas/odm/doctrine-mongo-mapping"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://doctrine-project.org/schemas/odm/doctrine-mongo-mapping
                    http://doctrine-project.org/schemas/odm/doctrine-mongo-mapping.xsd">

    <document
        name="App\Core\Domain\Aggregate\EventLog\EventLog"
        collection="EventLog">

        <id field-name="id" type="string" strategy="NONE"/>
        <field field-name="eventId" type="string" nullable="true"/>
        <field field-name="aggregateId" type="string" nullable="true"/>
        <field field-name="name" type="string" nullable="true"/>
        <field field-name="body" type="string" nullable="true"/>
        <field field-name="occurredOn" type="string" nullable="true"/>
        <field field-name="type" type="string" nullable="true"/>

    </document>
</doctrine-mongo-mapping>