<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mongo-mapping 
    xmlns="http://doctrine-project.org/schemas/odm/doctrine-mongo-mapping"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://doctrine-project.org/schemas/odm/doctrine-mongo-mapping
                    http://doctrine-project.org/schemas/odm/doctrine-mongo-mapping.xsd">

    <document
            name="App\Shared\Domain\Model\LiquidacionBase\LiquidacionBase"
            collection="LiquidacionBase" 
            inheritance-type="COLLECTION_PER_CLASS">

        <id field-name="id" type="string" strategy="NONE"/>
        
        <field field-name="createdAt" type="date" nullable="true"/>
        <field field-name="updatedAt" type="date" nullable="true"/>
        <field field-name="createdBy" type="string" nullable="true"/>
        <field field-name="updatedBy" type="string" nullable="true"/>
        <field field-name="deletedAt" type="date" nullable="true"/>

        <embed-one field="persona" target-document="App\Shared\Domain\Model\Persona\Persona" />        
        <embed-one field="sede" target-document="App\Shared\Domain\Model\Sede\Sede" nullable="true" />        

        <embed-one field="fechaCalculo" target-document="App\Shared\Domain\Model\LiquidacionBase\FechaCalculo" />
        <embed-one field="fechaLiquidacion" target-document="App\Shared\Domain\Model\LiquidacionBase\FechaLiquidacion" />
        <embed-one field="montoTotal" target-document="App\Shared\Domain\Model\LiquidacionBase\MontoTotal" />
        <embed-one field="ordenPagoId" target-document="App\Shared\Domain\Model\LiquidacionBase\OrdenPagoId" nullable="true" />
        <embed-one field="validoHasta" target-document="App\Shared\Domain\Model\LiquidacionBase\ValidoHasta" />

        <!--
        <embed-one field="distrito" target-document="App\Liquidacion\Domain\Model\Liquidacion\DistritoId" />
        <embed-many field="matriculas" target-document="App\Liquidacion\Domain\Model\Matricula\Matricula" />
        <embed-many field="items" target-document="App\Liquidacion\Domain\Model\Item\Item" />
        -->

        <discriminator-field name="type" />
        <discriminator-map>
            <discriminator-mapping value="matriculas" class="App\Liquidacion\Domain\Model\Liquidacion\Liquidacion" />
            <discriminator-mapping value="timbrados" class="App\LiquidacionTimbrado\Domain\Model\Liquidacion\Liquidacion" />
            <discriminator-mapping value="cep" class="App\LiquidacionCep\Domain\Model\Liquidacion\Liquidacion" />
        </discriminator-map>
    </document>

</doctrine-mongo-mapping>                
