<?php

namespace App\Shared\Infrastructure\ApiPlatform\State;

use ApiPlatform\Metadata\Operation;
use App\Core\Infrastructure\ApiPlatform\State\GenericAuditableProcessor;
use App\Shared\Application\Command\ReversaCommand;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Core\Infrastructure\ApiPlatform\State\GenericProcessor;
use App\Shared\Infrastructure\ApiPlatform\Resource\ReversaResource;

class ReversaProcessor extends GenericAuditableProcessor
{
    
    public function setCommand($data, Operation $operation, array $uriVariables = [], array $context = []): mixed
    {   
        $command = new ReversaCommand(
            $data->getReciboId(),
            $data->getModo(),
            $data->getPorcentajeGasto(),
            $data->getPorcentajeModulo()
        );

        return $command;

    }

    public function getResource($model)
    {
        $output = new ReversaResource();
        $output->setReciboId($model->id_recibo);
        $output->setMessage("generado correctamente");
        return $output;
        //return new JsonResponse("generado correctamente",201);//generar mensaje de  correcto , si no hubo inconvenientes
    }   
}