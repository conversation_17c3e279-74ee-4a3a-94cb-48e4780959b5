<?php

namespace App\Shared\Infrastructure\ApiPlatform\Resource;


use ApiPlatform\Metadata\Post;
use ApiPlatform\Metadata\ApiResource;
use App\Core\Infrastructure\ApiPlatform\Resource\ResourceAuditableInterface;
use Symfony\Component\Serializer\Annotation\Groups;
use App\Shared\Infrastructure\ApiPlatform\State\ReversaProcessor;


#[ApiResource(
    shortName: 'Reversa',
    normalizationContext: ['groups' => ['reversa:read']],
    operations: [
       new Post(
            uriTemplate: 'reversa/recibo/generar',
            shortName: 'Reversa',
            validationContext: ['groups' => ['reversa:read']],
            denormalizationContext: ['groups' => ['reversa:read']],
            processor: ReversaProcessor::class,
       )
    ],
)]
final class ReversaResource implements ResourceAuditableInterface {

    public function getId()
    {
        return $this->getReciboId() ?? '';
    }

    public static function isAuditable(): bool
    {
        return true;
    }

    public static function getModel(): string 
    {
        return 'Reversa';
    }

    #[Groups(['reversa:read'])]
    private string $reciboId;

    #[Groups(['reversa:read'])]
    private string $modo ;

    #[Groups(['reversa:read'])]
    private float $porcentajeGasto = 10.0 ;

    #[Groups(['reversa:read'])]
    private float $porcentajeModulo = 30.0;

    #[Groups(['reversa:read'])]
    private ?string $message = null;

    public function __construct(){}

    /**
     * Get the value of reciboId
     */ 
    public function getReciboId()
    {
        return $this->reciboId;
    }

    /**
     * Set the value of reciboId
     *
     * @return  self
     */ 
    public function setReciboId($reciboId)
    {
        $this->reciboId = $reciboId;

        return $this;
    }

    /**
     * Get the value of modo
     */ 
    public function getModo()
    {
        return $this->modo;
    }

    /**
     * Set the value of modo
     *
     * @return  self
     */ 
    public function setModo($modo)
    {
        $this->modo = $modo;

        return $this;
    }

    /**
     * Get the value of porcentajeGasto
     */ 
    public function getPorcentajeGasto()
    {
        return $this->porcentajeGasto;
    }

    /**
     * Set the value of porcentajeGasto
     *
     * @return  self
     */ 
    public function setPorcentajeGasto($porcentajeGasto)
    {
        $this->porcentajeGasto = $porcentajeGasto;

        return $this;
    }


    /**
     * Get the value of message
     */ 
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * Set the value of message
     *
     * @return  self
     */ 
    public function setMessage($message)
    {
        $this->message = $message;

        return $this;
    }

    /**
     * Get the value of porcentajeModulo
     */ 
    public function getPorcentajeModulo()
    {
        return $this->porcentajeModulo;
    }

    /**
     * Set the value of porcentajeModulo
     *
     * @return  self
     */ 
    public function setPorcentajeModulo($porcentajeModulo)
    {
        $this->porcentajeModulo = $porcentajeModulo;

        return $this;
    }
}