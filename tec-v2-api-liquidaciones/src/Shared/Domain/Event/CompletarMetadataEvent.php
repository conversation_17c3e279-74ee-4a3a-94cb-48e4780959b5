<?php

declare(strict_types=1);

namespace App\Shared\Domain\Event;

use App\Core\Domain\Event\DomainEvent;

class CompletarMetadataEvent extends DomainEvent
{
    public function __construct(
        private readonly string $id,
        private readonly string $itemLiquidacionId,
        private readonly ?string $identificador,
        private readonly string $circuito,
        private readonly string $liquidacionId,
        ?string $eventId = null,
        ?string $eventDate = null
    ) {
        parent::__construct($id, $eventId, $eventDate);
    }

    public static function eventName(): string
    {
        return 'orden_pago.metadata';
    }

    public static function fromPrimitives(
        string $aggregateId,
        array $data,
        string $eventId,
        string $eventDate
    ): self {
        return new static(
            $aggregateId,
            //$data['id'],
            $data['itemLiquidacionId'],
            $data['identificador'],
            $data['circuito'],
            $data['liquidacionId'],
            $eventId,
            $eventDate
        );
    }

    public function toPrimitives(): array
    {
        return [
            'id' => $this->id,
            'itemLiquidacionId' => $this->itemLiquidacionId,
            'identificador' => $this->identificador,
            'circuito' => $this->circuito,
            'liquidacionId' => $this->liquidacionId,
        ];
    }



        /**
         * Get the value of id
         */ 
        public function getId()
        {
                return $this->id;
        }

        /**
         * Get the value of itemLiquidacionId
         */ 
        public function getItemLiquidacionId()
        {
                return $this->itemLiquidacionId;
        }

        /**
         * Get the value of identificador
         */ 
        public function getIdentificador()
        {
                return $this->identificador;
        }

        /**
         * Get the value of circuito
         */ 
        public function getCircuito()
        {
                return $this->circuito;
        }

        public function getLiquidacionId()
        {
                return $this->liquidacionId;
        }
}
