<?php

namespace App\Shared\Domain\Event;

use App\Core\Domain\Event\DomainEvent;

abstract class ArancelPagoEvent extends DomainEvent
{
    public function __construct(
        private readonly string $id, //agregateRootId 
        private readonly string $matriculaId,
        private readonly string $key,
        private readonly ?string $reciboId = null,
        private readonly ?string $ordenPagoId = null,
        private readonly ?string $planPagoId = null,
        private readonly ?string $numero = null,
        private readonly ?float $importe = null,
        private readonly ?string $vencimiento = null,
        ?string $eventId = null,
        ?string $eventDate = null
    )
    {
        parent::__construct($id, $eventId, $eventDate);
    }

    public static function eventName(): string
    {
        return 'arancel.pago';
    }

    public static function fromPrimitives(
        string $aggregateId,
        array $data,
        string $eventId,
        string $eventDate
    ): self {
        return new static(
            $aggregateId,
            $data['matriculaId'],
            $data['key'],
            @$data['reciboId'],
            @$data['ordenPagoId'],
            @$data['planPagoId'],
            @$data['numero'],
            @$data['importe'],
            @$data['vencimiento'],
            $eventId,
            $eventDate
        );
    }

    public function toPrimitives(): array
    {
        return [
            'id' => $this->id,
            'matriculaId' => $this->matriculaId,
            'key' => $this->key,
            'reciboId' => @$this->reciboId,
            'ordenPagoId' => @$this->ordenPagoId,
            'planPagoId' => @$this->planPagoId,
            'numero' => @$this->numero,
            'importe' => @$this->importe,
            'vencimiento' => @$this->vencimiento,
        ];
    }

        /**
         * Get the value of id
         */ 
        public function getId()
        {
                return $this->id;
        }

        /**
         * Set the value of id
         *
         * @return  self
         */ 
        public function setId($id)
        {
                $this->id = $id;

                return $this;
        }

        /**
         * Get the value of matriculaId
         */ 
        public function getMatriculaId()
        {
                return $this->matriculaId;
        }

        /**
         * Set the value of matriculaId
         *
         * @return  self
         */ 
        public function setMatriculaId($matriculaId)
        {
                $this->matriculaId = $matriculaId;

                return $this;
        }

        /**
         * Get the value of key
         */ 
        public function getKey()
        {
                return $this->key;
        }

        /**
         * Set the value of key
         *
         * @return  self
         */ 
        public function setKey($key)
        {
                $this->key = $key;

                return $this;
        }

    /**
     * Get the value of reciboId
     */ 
    public function getReciboId()
    {
        return $this->reciboId;
    }

    /**
     * Get the value of ordenPagoId
     */ 
    public function getOrdenPagoId()
    {
        return $this->ordenPagoId;
    }

        /**
         * Get the value of planPagoId
         */ 
        public function getPlanPagoId()
        {
                return $this->planPagoId;
        }

        /**
         * Get the value of numero
         */ 
        public function getNumero()
        {
                return $this->numero;
        }

        /**
         * Get the value of importe
         */ 
        public function getImporte()
        {
                return $this->importe;
        }

        /**
         * Get the value of vencimiento
         */ 
        public function getVencimiento()
        {
                return $this->vencimiento;
        }
}