<?php

declare(strict_types=1);

namespace App\Shared\Domain\ValueObject;

class IdentificadoresConceptos {

    public const BONIFICACION_ANUAL = "bonificacion_anual";
    public const BONIFICACION_MULTIPLE = "bonificacion_multiple";
    public const MULTA = "multa" ;
    public const CUOTA = "cuota_matricular" ;
    public const NOTA_DEBITO = "nota_debito" ;
    public const NOTA_CREDITO = "nota_credito";

    public const GASTO_ADMINISTRATIVO = 'gasto_administrativo';
    public const ARANCEL_REHABILITACION = "arancel_rehabilitacion" ;
    public const ARANCEL_REINSCRIPCION = "arancel_reinscripcion";
    public const TIMBRADO_INTERNO_PREPAGO = "timbrado_interno_prepago";
    public const TIMBRADO_INTERNO_MANUAL = "timbrado_interno_manual";
    public const CEP = "cep";

    public const TIPO_ARANCEL = "arancel" ;
    public const TIPO_CUOTA = "cuota";
    public const TIPO_MULTA = "multa";
    public const TIPO_OTRO_CONCEPTO = "otro_concepto";
    public const TIPO_BONIFICACION = "bonificacion";
    public const TIPO_TIMBRADO = "timbrado";
    public const TIPO_CEP = "cep";

}