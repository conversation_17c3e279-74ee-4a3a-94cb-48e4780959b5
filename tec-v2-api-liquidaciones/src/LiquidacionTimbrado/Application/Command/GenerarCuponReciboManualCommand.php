<?php

namespace App\LiquidacionTimbrado\Application\Command;

use App\Core\Application\Command\CommandAuditable;

final class GenerarCuponReciboManualCommand extends CommandAuditable
{
    public function __construct(
        public readonly ?string $personaId = null,
        public readonly ?string $sedeId,
        public readonly array $items,
        public readonly array $pagos,
        public readonly array $notasCredito,
        public readonly \DateTime $fechaCalculo,
        public readonly string $modo,
        public readonly bool $generar, 
        public readonly string $circuito,
        public readonly ?array $preferenciaPagos = null,
        public readonly ?string $cuit = null,
        public readonly ?bool $manual = false,
    ){}
}