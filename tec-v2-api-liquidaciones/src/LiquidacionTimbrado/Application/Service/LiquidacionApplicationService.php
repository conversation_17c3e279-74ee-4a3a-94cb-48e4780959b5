<?php

namespace App\LiquidacionTimbrado\Application\Service;

use Psr\Log\LoggerInterface;
use Symfony\Component\Uid\Uuid;
use App\Core\Domain\Event\EventBus;
use App\Shared\Domain\Model\Sede\Sede;
use App\Shared\Domain\Model\Persona\Cuit;
use App\Shared\Domain\Model\Tarea\TareaId;
use App\Shared\Domain\Model\Titulo\Titulo;
use App\Shared\Domain\Model\Tarea\TareaEmb;
use App\Shared\Domain\Model\Modalidad\Sigla;
use App\Shared\Domain\Model\Persona\Persona;
use App\Shared\Domain\Model\Titulo\TituloId;
use App\Shared\Domain\Model\Categoria\Nombre;
use App\Shared\Domain\Model\Persona\Apellido;
use App\Shared\Domain\Model\Distrito\Distrito;
use App\Shared\Domain\Model\Persona\Documento;
use App\Shared\Domain\Model\Persona\PersonaId;
use App\Shared\Domain\Model\Categoria\Categoria;
use App\Shared\Domain\Model\Modalidad\Modalidad;
use App\Shared\Domain\Model\SedeCabecera\Codigo;
use App\Shared\Domain\Model\Distrito\Abreviatura;
use App\Shared\Domain\Model\Categoria\CategoriaId;
use App\Shared\Domain\Model\Modalidad\ModalidadId;
use App\Shared\Domain\Model\ObjectIdName\ObjectId;
use App\Shared\Domain\Model\SedeCabecera\Contacto;
use App\LiquidacionTimbrado\Domain\Model\Item\Item;
use App\Shared\Domain\Model\Tarea\PorcentajeAporte;
use App\Shared\Domain\Model\Distrito\NombreDistrito;
use App\Shared\Domain\Model\ObjectIdName\ObjectName;
use App\Shared\Domain\Model\SedeCabecera\NombreSede;
use App\LiquidacionTimbrado\Domain\Model\Item\ItemId;
use App\LiquidacionTimbrado\Domain\Model\Legajo\Tipo;
use App\Shared\Domain\Model\Modalidad\NombreModalidad;
use App\Shared\Domain\Model\ObjectIdName\ObjectIdName;
use App\Shared\Domain\Model\SedeCabecera\SedeCabecera;
use App\LiquidacionTimbrado\Domain\Model\Item\Alicuota;
use App\LiquidacionTimbrado\Domain\Model\Item\Comision;
use App\LiquidacionTimbrado\Domain\Model\Legajo\Legajo;
use App\Shared\Domain\Model\LiquidacionBase\MontoTotal;
use App\Shared\Domain\Model\LiquidacionBase\ValidoHasta;
use App\Shared\Domain\Model\SedeCabecera\SedeCabeceraId;
use App\Shared\Domain\Model\Tarea\Nombre as TareaNombre;
use App\LiquidacionTimbrado\Domain\Model\Item\Referencia;
use App\LiquidacionTimbrado\Domain\Model\Legajo\LegajoId;
use App\Shared\Domain\Model\LiquidacionBase\FechaCalculo;
use App\LiquidacionTimbrado\Domain\Model\Item\Descripcion;
use App\Shared\Domain\Model\LiquidacionBase\LiquidacionId;
use App\Shared\Domain\Model\Titulo\Codigo as TituloCodigo;
use App\LiquidacionTimbrado\Domain\Event\TimbradoPagoEvent;
use App\LiquidacionTimbrado\Domain\Model\Item\ImporteTotal;
use App\Shared\Domain\ValueObject\ConfiguracionesTimbrados;
use App\Shared\Domain\ValueObject\IdentificadoresConceptos;
use App\LiquidacionTimbrado\Domain\Model\Item\Identificador;
use App\LiquidacionTimbrado\Domain\Model\Item\MontoAlicuota;
use App\LiquidacionTimbrado\Domain\Model\Item\MontoComision;
use App\Shared\Domain\Model\LiquidacionBase\LiquidacionBase;
use App\Shared\Domain\Model\Persona\Nombre as PersonaNombre;
use App\LiquidacionTimbrado\Domain\Model\Legajo\NumeroLegajo;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\Fecha;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\Monto;
use App\LiquidacionTimbrado\Domain\Model\Matricula\Matricula;
use App\Shared\Domain\Model\LiquidacionBase\FechaLiquidacion;
use App\Core\Application\Service\ApiConnectionServiceInterface;
use App\LiquidacionTimbrado\Domain\Model\Matricula\MatriculaId;
use App\LiquidacionTimbrado\Domain\Model\Liquidacion\DistritoId;
use App\Shared\Application\Service\SharedHttpApplicationService;
use App\LiquidacionTimbrado\Domain\Model\Item\Fecha as ItemFecha;
use App\LiquidacionTimbrado\Domain\Model\Liquidacion\Liquidacion;
use App\LiquidacionTimbrado\Domain\Event\TimbradoPagoAnuladoEvent;
use App\Shared\Domain\Model\LegajoDetalleTarea\LegajoDetalleTarea;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\Vencimiento;
use App\LiquidacionTimbrado\Domain\Model\Matricula\CodigoMatricula;
use App\LiquidacionTimbrado\Domain\Model\Matricula\NumeroMatricula;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\TipoContrato;
use App\Shared\Domain\Model\LegajoDetalleTarea\LegajoDetalleTareaId;
use App\Shared\Domain\Model\Persona\DistritoId as PersonaDistritoId;
use App\Shared\Domain\Model\Titulo\Descripcion as TituloDescripcion;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\LegajoDetalle;
use App\Shared\Application\Service\LiquidacionBaseApplicationService;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\NumeroContrato;
use App\Shared\Domain\Model\Distrito\DistritoId as DistritoDistritoId;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\LegajoDetalleId;
use App\LiquidacionTimbrado\Domain\Model\LegajoDetalle\MontoDiferenciado;
use App\LiquidacionTimbrado\Domain\Model\Item\Vencimiento as ItemVencimiento;
use App\LiquidacionTimbrado\Domain\Repository\LiquidacionRepositoryInterface;
use App\Shared\Domain\Model\LegajoDetalleTarea\Monto as LegajoDetalleTareaMonto;
use App\Shared\Domain\Model\SedeCabecera\Abreviatura as SedeCabeceraAbreviatura;

final class LiquidacionApplicationService extends LiquidacionBaseApplicationService
{
    public function __construct(
        LiquidacionRepositoryInterface $repository, 
        SharedHttpApplicationService $httpService,
        LoggerInterface $logger,
        private ApiConnectionServiceInterface $apiConnection,
        private EventBus $asyncBus,
    ) {
        parent::__construct($httpService, $repository, $logger);
    }

    /**
     *  Metodo que permite obtener la deuda/importe
     * 
     * @param $items items de deuda que se quieren recalcular
     * @param $id identificador del detalle
     * @param $args data extra para el circuito
     * 
     * @return $deuda retorna un array con la deuda/importe 
     */
    public function obtenerDeuda($items, $id, $fecha, array $args = [])
    {
        $deuda = [];    
        
        // Validar que existan items de Detalles
        if (count($items) == 0)
        {
            $objetoId = isset($args['objetoId']) ? $args['objetoId'] : null;

            if (!$objetoId) return $deuda; 

            $prepago = $args['prepago'];
            $sedeId = $args['sedeId'];
    
            $sede = $sedeId ? $this->httpService->obtenerSede($sedeId) : null;
            $concepto = ($prepago) ? $this->httpService->findConceptoByKey(IdentificadoresConceptos::TIMBRADO_INTERNO_PREPAGO) : $this->httpService->findConceptoByKey(IdentificadoresConceptos::TIMBRADO_INTERNO_MANUAL);

            $legajoDetalle = $this->httpService->obtenerLegajoDetalle($objetoId);

            $data = $this->getDataTimbrado($legajoDetalle, $sede, $prepago);
            //$data['persona'] = $this->getDataPersona($persona);
            $data['concepto'] = $this->getDataConcepto($concepto[0]);

            $deuda[] = (object) $data;
        } else {
            $deuda = $this->validarDeuda($deuda, $items, $id, $args);
        }

        return $deuda ;
    }

    public function calcularLiquidacion($items, $fecha_para_calcular = new \DateTime('now'), array $args = [])
    {
        return $items;
    }

    public function validarLiquidacion(LiquidacionBase $liquidacion): array
    {
        return [] ;
    }

    /**
    * Metodo que valida la dueda , comparando la preLiquidacion con la deuda y devuelve los elementos de deuda para luego hacer el recalculo
    * @param $deuda deuda de la persona que se obtiene del servicio cuotas 
    * @param $items
    * @param $persona_id identificador de la persona a la cual se le calcula la preLiquidacion 
    */
    public function validarDeuda($deuda, $items, $id, array $args = [])
    {
        $idDetallesATimbrar = [];
        $deuda_validada = [];
        foreach ($items as $item)
        {
            switch ($item->concepto->tipo_item->key) 
            {
                case IdentificadoresConceptos::TIPO_TIMBRADO:
                    // Recalculo y validacion (fecha, sedes, etc)
                    $itemRecalculado = $this->recalcular($item, $args);

                    if ($this->buscarIdRepetido($idDetallesATimbrar, $item->identificador)) throw new BadRequestHttpException("IDs repetidos");
                    $idDetallesATimbrar[] = $item->identificador;

                    $deuda_validada[] = $itemRecalculado;
                    break;
                case IdentificadoresConceptos::TIPO_OTRO_CONCEPTO:
                case IdentificadoresConceptos::TIPO_BONIFICACION:
                    $deuda_validada[] = $this->generarConceptoOtro($item);
                    break;
            }
        }

        return $deuda_validada ;
    }

    public function validarItems($liquidacion, $orden_pago, &$nota_credito, &$monto_nota, $circuito)
    {
        $valido = true;

        //validar que los items de la liquidacion , esten en condiciones
        foreach($liquidacion->getItems() as $item)
        {
            $tipo_concepto_key = $item->getConcepto()->getTipoItem()->getKey()->getValue();
            switch ($tipo_concepto_key) {
                case IdentificadoresConceptos::TIPO_TIMBRADO:
                    $valido = $this->validarItem($item);
                    break;
            }

            //sino estan en condiciones, generar nota credito
            if (!$valido)
            {
                $nota_credito = true;
                $monto_nota = $orden_pago->monto_total;
                return $valido;
            }
        }

        return $valido;
    }

    public function validarItem(Item $item)
    {
        try {
            $legajoDetalle = $this->httpService->obtenerLegajoDetalle($item->getIdentificador()->getValue());
            return $legajoDetalle->legajo->puede_procesar_timbrado;
        } catch (\Throwable $th) {
            $this->logger->error("Error al validar item: ".$th->__toString());
            return false;
        }
    }

    public function recalcular($item, $args) 
    {
        $legajoDetalle = $this->httpService->obtenerLegajoDetalle($item->identificador);

        // Validar que esté en fecha de timbrado
        /*
        if ($item->prepago) {
            // if (!$legajoDetalle->puede_timbrarse_prepago) throw new BadRequestHttpException("No es posible timbrar (prepago) el detalle ".$legajoDetalle->id);
        } else {
            if (!$legajoDetalle->puede_timbrarse_interno) throw new BadRequestHttpException("No es posible timbrar el detalle ".$legajoDetalle->id);
        }
        */

        // Validacion de la sede
        $sedeId = $args['sedeId'];
        $sede = $sedeId ? $this->httpService->obtenerSede($sedeId) : null;
        $accionesSede = $this->httpService->accionesPosiblesSede($sede->id);
        if (!$accionesSede->puede_timbrar) throw new BadRequestHttpException("La sede no esta habilitada para timbrar.");

        // Recuperar concepto 
        $concepto = ($item->prepago) ? $this->httpService->findConceptoByKey(IdentificadoresConceptos::TIMBRADO_INTERNO_PREPAGO) : $this->httpService->findConceptoByKey(IdentificadoresConceptos::TIMBRADO_INTERNO_MANUAL);

        $data = $this->getDataTimbrado($legajoDetalle, $sede, $item->prepago);

        $data['concepto'] = $this->getDataConcepto($concepto[0]);

        return (object) $data;
    }

    public function lanzarEventos(LiquidacionBase $liquidacion, ?string $reciboId = null, ?string $ordenPagoId = null)
    {
        $this->logger->info("Se procede a lanzar eventos...");
        /** @var Item $item */
        foreach($liquidacion->getItems() as $item)
        {
            switch($item->getConcepto()->getKey()->getValue()) {
                case IdentificadoresConceptos::TIMBRADO_INTERNO_MANUAL:
                case IdentificadoresConceptos::TIMBRADO_INTERNO_PREPAGO:
                    $this->logger->info("Record evento...");
                    $liquidacion->recordDomainEvent($this->eventoTimbrado($item, $liquidacion, $reciboId, $ordenPagoId));
                    break; 
                default:
                    break;
            }
        }
        // Publicar eventos
        $this->asyncBus->publish(...$liquidacion->pullDomainEvents());
    }

    public function lanzarEventosDesacritacion($recibo,$reversa){
        
        $liquidacion = $this->repository->findByOrdenPagoId($recibo->orden_pago);

        $events = null;
        foreach ($liquidacion->getItems() as $item) 
        {
            if ($item->getConcepto()->getKey()->getValue() == IdentificadoresConceptos::TIMBRADO_INTERNO_MANUAL || 
                $item->getConcepto()->getKey()->getValue() == IdentificadoresConceptos::TIMBRADO_INTERNO_PREPAGO)
            {
                $events[] = $this->eventoTimbradoPagoAnulado($item, $liquidacion, $recibo->id);
            }
        }

        // Publicar eventos
        if ($events) $this->asyncBus->publish(...$events); 
    }

    public function validacionPuedeReversa($recibo)
    {
        $liquidacion = $this->repository->findByOrdenPagoId($recibo->orden_pago);

        if ($liquidacion == null) return false;

        $check = true;
        foreach ($liquidacion->getItems() as $item) 
        {
            if ($item->getConcepto()->getKey()->getValue() == IdentificadoresConceptos::TIMBRADO_INTERNO_MANUAL || 
                $item->getConcepto()->getKey()->getValue() == IdentificadoresConceptos::TIMBRADO_INTERNO_PREPAGO)
            {
                $puedeAnular = $this->httpService->puedeAnularTimbrado($item->getIdentificador()->getValue(), $item->getReferencia()->getValue());
                if ($puedeAnular->result == false) {
                    $check = false;
                    break;
                }
            }
        }

        return $check;
    }

    public function eventoTimbradoPagoAnulado($item,$liquidacion,$reciboId){

        return new TimbradoPagoAnuladoEvent(
            $liquidacion->getId(),
            $item->getReferencia()->getValue(),
            $item->getIdentificador()->getValue(), 
            $reciboId
        ); 
    }

    private function eventoTimbrado(Item $item, LiquidacionBase $liquidacion, ?string $reciboId, ?string $ordenPagoId)
    {
        $fecha = new \DateTime('NOW');

        $event = new TimbradoPagoEvent(
            $liquidacion->getId(), 
            $item->getReferencia()->getValue(), // ID a aplicar
            $fecha->format('Y-m-d H:i:s'),
            $item->getImporteTotal()->getValue(),
            $item->getPrepago(),
            $item->getIdentificador()->getValue(), // legajo detalle
            $liquidacion->getSede()->getId()->getValue(),
            $item->getDescripcion() ? $item->getDescripcion()->getValue() : null,
            $item->getComision() ? $item->getComision()->getValue() : null,
            $item->getAlicuota() ? $item->getAlicuota()->getValue() : null,
            intval($fecha->format('Y')),
            $item->getMismoPartido(),
            $liquidacion->getPersona()->getId()->getValue(),
            $item->getObjeto()->getLegajo()->getMatricula()?$item->getObjeto()->getLegajo()->getMatricula()->getId():null,
            $liquidacion->getCreatedBy(),
            $reciboId,
            $ordenPagoId,
        );

        $this->logger->info("Evento Timbrado generado: ".json_encode($event->toPrimitives()));

        return $event;
    }

    public function obtenerObjetoAuxiliar($item, bool $array = false)
    {
        return null;
    }


    public function crearItemLiquidacion($item) 
    {
        // Generico para conceptos, y especifico para timbrado 
        $it = array(
            'id' => new ItemId(Uuid::v4()->__toString()),
            'importeTotal' => new ImporteTotal($item->importe_total),
            'descripcion' => new Descripcion($item->descripcion),
            'fecha' => isset($item->fecha) ? new ItemFecha($item->fecha) : null,
            'concepto' => $this->createTipoConcepto($item->concepto),
        );

        if ($item->concepto->tipo_item->key == IdentificadoresConceptos::TIPO_TIMBRADO)
        {
            $tareas = null;
            if (isset($item->objeto->tareas))
            {
                foreach ($item->objeto->tareas as $t) {
                    $tareas[] = new LegajoDetalleTarea(
                        new LegajoDetalleTareaId($t->id),
                        new LegajoDetalleTareaMonto(@$t->monto),
                        new TareaEmb(
                            new TareaId($t->tarea->id),
                            new TareaNombre(@$t->tarea->nombre),
                            new PorcentajeAporte(@$t->tarea->porcentaje_aporte),
                            @$t->tarea->cep_porcentaje_diferenciado
                        )
                    );
                }
            }

            $itTimbrado = [
                'identificador' => new Identificador($item->identificador),
                'referencia' => new Referencia($item->referencia),
                'alicuota' => new Alicuota($item->alicuota),
                'montoAlicuota' => new MontoAlicuota($item->monto_alicuota),
                'comision' => new Comision($item->comision),
                'montoComision' => new MontoComision($item->monto_comision),
                'prepago' => $item->prepago,
                'mismoPartido' => $item->mismo_partido,
                'vencimiento' => new ItemVencimiento($item->vencimiento),
                'objeto' => new LegajoDetalle(
                    new LegajoDetalleId($item->objeto->id),
                    new Fecha(new \DateTime($item->objeto->fecha)),
                    new Vencimiento(new \DateTime($item->objeto->vencimiento)),
                    new Monto($item->objeto->monto),
                    new Legajo(
                        new LegajoId($item->objeto->legajo->id),
                        new Tipo($item->objeto->legajo->tipo),
                        new NumeroLegajo($item->objeto->legajo->numero_legajo),
                        isset($item->objeto->legajo->persona) ? new Persona(
                            new PersonaId(@$item->objeto->legajo->persona->id),
                            new PersonaNombre(@$item->objeto->legajo->persona->nombre),
                            new Documento(@$item->objeto->legajo->persona->documento),
                            new Apellido(@$item->objeto->legajo->persona->apellido),
                            new PersonaDistritoId(@$item->objeto->legajo->persona->distrito_id),
                            new Cuit(@$item->objeto->legajo->persona->cuit),
                        ) : null,
                        isset($item->objeto->legajo->matricula) ? new Matricula(
                            new MatriculaId($item->objeto->legajo->matricula->id),
                            new NumeroMatricula(@$item->objeto->legajo->matricula->numero),
                            new CodigoMatricula(@$item->objeto->legajo->matricula->codigo),
                            isset($item->objeto->legajo->matricula->titulo) ? new Titulo(
                                new TituloId($item->objeto->legajo->matricula->titulo->id),
                                new TituloDescripcion(@$item->objeto->legajo->matricula->titulo->descripcion),
                                new TituloCodigo(@$item->objeto->legajo->matricula->titulo->codigo),
                                isset($item->objeto->legajo->matricula->titulo->rubro) ? new ObjectIdName(
                                    new ObjectId($item->objeto->legajo->matricula->titulo->rubro->id),
                                    new ObjectName($item->objeto->legajo->matricula->titulo->rubro->nombre),
                                ) : null
                            ) : null,
                            isset($item->objeto->legajo->matricula->institucion) ? new ObjectIdName(
                                new ObjectId($item->objeto->legajo->matricula->institucion->id),
                                new ObjectName(@$item->objeto->legajo->matricula->institucion->nombre),
                            ) : null,
                            isset($item->objeto->legajo->matricula->categoria) ? new Categoria(
                                new CategoriaId($item->objeto->legajo->matricula->categoria->id),
                                new Nombre(@$item->objeto->legajo->matricula->categoria->nombre),
                                isset($item->objeto->legajo->matricula->categoria->tipo_categoria) ? new ObjectIdName(
                                    new ObjectId($item->objeto->legajo->matricula->categoria->tipo_categoria->id),
                                    new ObjectName(@$item->objeto->legajo->matricula->categoria->tipo_categoria->nombre),
                                ) : null
                            ) : null,
                        ) : null,
                        isset($item->objeto->legajo->distrito) ? new Distrito(
                            new DistritoDistritoId($item->objeto->legajo->distrito->id),
                            new NombreDistrito($item->objeto->legajo->distrito->nombre),
                            new Abreviatura($item->objeto->legajo->distrito->abreviatura),
                            isset($item->objeto->legajo->distrito->sede_cabecera) ? new SedeCabecera(
                                new SedeCabeceraId($item->objeto->legajo->distrito->sede_cabecera->id),
                                new NombreSede($item->objeto->legajo->distrito->sede_cabecera->nombre),
                                new SedeCabeceraAbreviatura($item->objeto->legajo->distrito->sede_cabecera->abreviatura),
                                new Contacto($item->objeto->legajo->distrito->sede_cabecera->contacto),
                                isset($item->objeto->legajo->distrito->sede_cabecera->partido) ? new ObjectIdName(
                                    new ObjectId($item->objeto->legajo->distrito->sede_cabecera->partido->id),
                                    new ObjectName(@$item->objeto->legajo->distrito->sede_cabecera->partido->nombre),
                                ) : null,
                                new Codigo($item->objeto->legajo->distrito->sede_cabecera->codigo),
                            ) : null
                        ) : null,
                        isset($item->objeto->legajo->modalidad) ? new Modalidad(
                            new ModalidadId(@$item->objeto->legajo->modalidad->id),
                            new NombreModalidad(@$item->objeto->legajo->modalidad->nombre),
                            new Sigla(@$item->objeto->legajo->modalidad->sigla),
                            @$item->objeto->legajo->modalidad->privada
                        ) : null,
                        isset($item->objeto->legajo->rubro) ? new ObjectIdName(
                            new ObjectId($item->objeto->legajo->rubro->id),
                            new ObjectName(@$item->objeto->legajo->rubro->nombre),
                        ) : null,
                    ),
                    new TipoContrato($item->objeto->tipo_contrato),
                    new MontoDiferenciado(@$item->objeto->monto_diferenciado),
                    new NumeroContrato(isset($item->objeto->numero_contrato) ? intval($item->objeto->numero_contrato) : 0),
                    $tareas
                )
            ];
            $itTimbrado['metadata'] = isset($item->objeto) ? json_encode($item->objeto) : null;

            $it = array_merge($it, $itTimbrado);
        }

        return $it ;
    }

    public function crearItemLiquidacionArray($item)
    {
        // Generico para conceptos, y especifico para timbrado 
        $it = [
            'id' => isset($item->id) ? $item->id : Uuid::v4()->__toString(),
            'importe_total' => $item->importe_total, //$montoAlicuota + $montoComision,
            'descripcion' => $item->descripcion,
            'fecha' => isset($item->fecha) ? $item->fecha : null,
            'concepto' => $this->createTipoConceptoArray($item->concepto),
        ];

        if ($it['concepto']['tipo_item']['key'] == IdentificadoresConceptos::TIPO_TIMBRADO)
        {
            $objeto = $this->getDataLegajoDetalle($item->objeto);
            $itTimbrado = [
                'alicuota' => isset($item->alicuota) ? $item->alicuota : null,
                'monto_alicuota' => isset($item->monto_alicuota) ? $item->monto_alicuota : null,
                'comision' => isset($item->comision) ? $item->comision : null,
                'monto_comision' => isset($item->monto_comision) ? $item->monto_comision : null,
                'prepago' => isset($item->prepago) ? $item->prepago : null,
                'mismo_partido' => isset($item->mismo_partido) ? $item->mismo_partido : null,
                'identificador' => isset($item->identificador) ? $item->identificador : null,
                'vencimiento' => isset($item->vencimiento) ? $item->vencimiento : null,
                'referencia' => isset($item->referencia) ? $item->referencia : null,
                'objeto' => $objeto,
                'metadata' => json_encode($objeto),
            ];  

            $it = array_merge($it, $itTimbrado);
        }  

        return $it ;
    }

    public function crearObjetoLiquidacion(
        LiquidacionId $liquidacionId, Persona $persona, ?string $distrito, 
        ValidoHasta $validoHasta, MontoTotal $montoTotal,
        array $items, array $objetosAuxiliares, ?Sede $sede = null, ?string $createdBy = null
    )
    {
        // Reemplazar la fecha de validez al circuito
        $validoHasta = null;
        $checkConceptoTimbrado = false;
        foreach ($items as $i) {
            if ($i['concepto']->getTipoItem()->getKey()->getValue() == IdentificadoresConceptos::TIPO_TIMBRADO)
            {
                $checkConceptoTimbrado = true;
                if (!$validoHasta) $validoHasta = new ValidoHasta($i['vencimiento']->getValue());
                elseif ($i['vencimiento']->getValue() < $validoHasta->getValue()) $validoHasta = new ValidoHasta($i['vencimiento']->getValue());
            }          
        }

        //if (!$checkConceptoTimbrado) throw new BadRequestHttpException("No se detectó un concepto timbrado.");
        if (!$validoHasta) $validoHasta = new ValidoHasta((new \DateTime())->modify('+1 month'));

        /*
        $id_matriculas = [] ;
        $matriculas = [] ;
        foreach ($items as $it) 
        {
            if ($it['concepto']['tipo_item']['key'] == IdentificadoresConceptos::TIPO_TIMBRADO)
            {
                $legajoDetalle = $this->httpService->obtenerLegajoDetalle($it["identificador"]);
                $matricula = $legajoDetalle->legajo->matricula;
                
                if(!in_array($matricula->id, $id_matriculas))
                {
                    $id_matriculas[] = $matricula->id ;
                    //$mat = $this->httpService->obtenerMatricula($matricula->id);
                
                    $matriculas[] = [
                        'id' => $matricula->id,
                        'codigo' => $matricula->codigo,
                        'tituloId' => $matricula->titulo->id,
                        'idSistemaAnterior' => @$matricula->id_sistema_anterior
                    ];
                }
            }
        }
        */
        $liquidacion = Liquidacion::create(
            $liquidacionId,
            $persona,
            new DistritoId($distrito),
            $items,
            new FechaCalculo(new \DateTime('NOW')),
            new FechaLiquidacion(new \DateTime('NOW')),
            $validoHasta, //new ValidoHasta($fecha_valido instanceof DateTime ? $fecha_valido : new DateTime($fecha_valido)),
            null, 
            $montoTotal,
            $sede,
            $createdBy
        );

        return $liquidacion;
    }

    public function crearArrayLiquidacion(
        array $persona, ?string $distrito, 
        \DateTime $validoHasta, float $montoTotal,
        array $items, array $objetosAuxiliares, ?Sede $sede = null
    )
    {

        $validoHasta = null;
        $id_matriculas = [];
        $matriculas = [];
        $checkConceptoTimbrado = false;
        foreach ($items as $it) 
        {
            if ($it['concepto']['tipo_item']['key'] == IdentificadoresConceptos::TIPO_TIMBRADO)
            {
                $checkConceptoTimbrado = true;
                $legajoDetalle = $this->httpService->obtenerLegajoDetalle($it["identificador"]);
                $matricula = isset($legajoDetalle->legajo->matricula) ? $legajoDetalle->legajo->matricula : null;
                
                if($matricula && !in_array($matricula->id, $id_matriculas))
                {
                    $id_matriculas[] = $matricula->id ;
                
                    $matriculas[] = [
                        'id' => $matricula->id,
                        'codigo' => $matricula->codigo,
                        'tituloId' => $matricula->titulo->id,
                        'idSistemaAnterior' => @$matricula->id_sistema_anterior
                    ];
                }

                // Reemplazar la fecha de validez al circuito
                if (!$validoHasta) $validoHasta = $it['vencimiento'];
                elseif ($it['vencimiento'] < $validoHasta) $validoHasta = $it['vencimiento'];
            }
        }

        //if (!$checkConceptoTimbrado) throw new BadRequestHttpException("No se detectó un concepto timbrado.");
        //$validoHasta = new \DateTime();

        $liquidacion = [
            'persona' => $persona,
            'distrito' => $distrito,
            'items' => $items,
            'fechaCalculo' => new \DateTime('NOW'),
            'fechaLiquidacion' => new \DateTime('NOW'),
            'validoHasta' => $validoHasta,
            'ordenPagoId' => null, 
            'montoTotal' => $montoTotal,
            'matriculas' => $matriculas,
            //'sede' => $sede
        ];

        return $liquidacion;
    }

    // ############################ FUNCIONES PROPIAS DEL CIRCUITO ############################

    private function buscarIdRepetido($array, $id) 
    {
        foreach ($array as $v) {
            if ($v == $id) return true;
        }
    }

    /*
    * Genera un timbrado precargado con los valores de 
    * monto, alicuota y comision comparando el partido del 
    * inmueble de la ficha maestro de ese detalle
    * contra el partido de la sede en que está operando
    */
    private function getDataTimbrado($legajoDetalle, $sede, $prepago)
    {
        $partidoLegajo = $legajoDetalle->legajo->inmuebles[0]->localidad->partido->id;
        $partidoSede = ($sede != null) ? $sede->partido_id : null;
        $mismoPartido = false;

        if ($partidoLegajo == $partidoSede)
        {
            $alicuota = floatval(ConfiguracionesTimbrados::CONFIGURACIONES['ep_alicuota_igual_partido']['valor']); 
            $comision = floatval(ConfiguracionesTimbrados::CONFIGURACIONES['ep_comision_igual_partido']['valor']); 
            $mismoPartido = true;
        } else 
        {
            $alicuota = floatval(ConfiguracionesTimbrados::CONFIGURACIONES['ep_alicuota_distinto_partido']['valor']); 
            $comision = floatval(ConfiguracionesTimbrados::CONFIGURACIONES['ep_comision_distinto_partido']['valor']); 
        }

        if ($prepago) {
            $vencimiento = new \DateTime($legajoDetalle->fecha_vencimiento_timbrado_prepago);
        } else {
            $vencimiento = new \DateTime($legajoDetalle->fecha_vencimiento_timbrado_interno);
        }

        $numeroLegajo= $legajoDetalle->legajo->numero_legajo;
        $numeroContrato= isset($legajoDetalle->numero_contrato) ? $legajoDetalle->numero_contrato : "";
        $descripcion = "TIMBRADO CONTRATO #".$numeroLegajo."-".$numeroContrato;

        $montoAlicuota = $legajoDetalle->monto * ($alicuota / 100);
        $montoComision = $legajoDetalle->monto * ($comision / 100);

        $dataLegajoDetalle = $this->getDataLegajoDetalle($legajoDetalle);
       
        return [
            'objeto' => $dataLegajoDetalle,
            'identificador' => $legajoDetalle->id,
            'referencia' => Uuid::v4()->__toString(),
            'descripcion' => $descripcion,
            'vencimiento' => $vencimiento,
            'sede' => $sede,
            'alicuota' => $alicuota,
            'monto_alicuota' => $montoAlicuota,
            'comision' => $comision,
            'monto_comision' => $montoComision,
            'importe_total' => ceil($montoAlicuota + $montoComision),
            'prepago' => $prepago,
            'mismo_partido' => $mismoPartido,
        ];
    }

    private function getDataLegajoDetalle($legajoDetalle)
    {
        $tareas = null;

        if (isset($legajoDetalle->tareas)) 
        {
            foreach ($legajoDetalle->tareas as $t) {
                $tareas[] = (object) [
                    'id' => $t->id,
                    'cep_calculo_diferenciado' => @$t->cep_calculo_diferenciado,
                    'monto' => @$t->monto,
                    'tarea' => (object) [
                        'id' => $t->tarea->id,
                        'nombre' => @$t->tarea->nombre,
                        'cep_porcentaje_diferenciado' => @$t->tarea->cep_porcentaje_diferenciado,
                        'porcentaje_aporte' => @$t->tarea->porcentaje_aporte,
                    ]
                ];
            }
        }
        

        if (isset($legajoDetalle->legajo->distrito))
        {
            $distrito = (object) [
                'id' => $legajoDetalle->legajo->distrito->id,
                'nombre' => @$legajoDetalle->legajo->distrito->nombre,
                'abreviatura' => (isset($legajoDetalle->legajo->distrito->abreviatura)) ? $legajoDetalle->legajo->distrito->abreviatura : null,
                'sede_cabecera' => (object) [
                    'id' => $legajoDetalle->legajo->distrito->sede_cabecera->id,
                    'nombre' => @$legajoDetalle->legajo->distrito->sede_cabecera->nombre,
                    'abreviatura' => @$legajoDetalle->legajo->distrito->sede_cabecera->abreviatura,
                    'contacto' => @$legajoDetalle->legajo->distrito->sede_cabecera->contacto,
                    'codigo' => @$legajoDetalle->legajo->distrito->sede_cabecera->codigo,
                    'partido' => (object) [
                        'id' => $legajoDetalle->legajo->distrito->sede_cabecera->partido->id,
                        'nombre' => @$legajoDetalle->legajo->distrito->sede_cabecera->partido->nombre,
                    ],
                ],
            ];
        } else { $distrito = null; }

        if (isset($legajoDetalle->legajo->comitentes))
        {
            foreach ($legajoDetalle->legajo->comitentes as $c) {
                $comitentes[] = (object) [
                    'id' => $c->id,
                    'locador' => @$c->locador,
                    'apellido_nombre' => @$c->apellido_nombre,
                    'nombre' => @$c->nombre,
                    'apellido' => @$c->apellido,
                    'documento' => @$c->documento,
                    'cuit' => @$c->cuit,
                    'manual' => @$c->manual,
                    'fisico' => @$c->fisico,
                ];
            }
        } else { $comitentes = null; }

        return (object) [
            'id' => $legajoDetalle->id,
            'fecha' => $legajoDetalle->fecha,
            'vencimiento' => $legajoDetalle->vencimiento,
            'monto' => @$legajoDetalle->monto,
            'monto_diferenciado' => @$legajoDetalle->monto_diferenciado,
            'tipo_contrato' => @$legajoDetalle->tipo_contrato,
            'numero_contrato' => isset($legajoDetalle->numero_contrato) ? $legajoDetalle->numero_contrato : 0,
            'tareas' => $tareas,
            'legajo' => (object) [
                'id' => $legajoDetalle->legajo->id,
                'numero_legajo' => $legajoDetalle->legajo->numero_legajo,
                'tipo' => $legajoDetalle->legajo->tipo,
                'modalidad' => isset($legajoDetalle->legajo->modalidad) ? (object) [
                    'id' => $legajoDetalle->legajo->modalidad->id,
                    'nombre' => @$legajoDetalle->legajo->modalidad->nombre,
                    'sigla' => @$legajoDetalle->legajo->modalidad->sigla,
                    'privada' => @$legajoDetalle->legajo->modalidad->privada,
                ] : null,
                'persona' => isset($legajoDetalle->legajo->persona) ? (object) [
                    'id' => $legajoDetalle->legajo->persona->id,
                    'nombre' => $legajoDetalle->legajo->persona->nombre,
                    'apellido' => $legajoDetalle->legajo->persona->apellido,
                    'cuit' => @$legajoDetalle->legajo->persona->cuit,
                    'documento' => $legajoDetalle->legajo->persona->documento,
                    'distrito_id' => @$legajoDetalle->legajo->persona->distrito_id,
                ] : null,
                'matricula' => isset($legajoDetalle->legajo->matricula) ? (object) [
                    'id' => $legajoDetalle->legajo->matricula->id,
                    'numero' => @$legajoDetalle->legajo->matricula->numero,
                    'codigo' => @$legajoDetalle->legajo->matricula->codigo,
                    'titulo' => (object) [
                        'id' => $legajoDetalle->legajo->matricula->titulo->id,
                        'descripcion' => @$legajoDetalle->legajo->matricula->titulo->descripcion,
                        'codigo' => @$legajoDetalle->legajo->matricula->titulo->codigo,
                        'rubro' => (object) [
                            'id' => $legajoDetalle->legajo->matricula->titulo->rubro->id,
                            'nombre' => @$legajoDetalle->legajo->matricula->titulo->rubro->nombre,
                        ]
                    ],
                    'institucion' => (object) [
                        'id' => $legajoDetalle->legajo->matricula->institucion->id,
                        'nombre' => @$legajoDetalle->legajo->matricula->institucion->nombre,
                    ],
                    'categoria' => (object) [
                        'id' => $legajoDetalle->legajo->matricula->categoria->id,
                        'nombre' => @$legajoDetalle->legajo->matricula->categoria->nombre,
                        'tipo_categoria' => (object) [
                            'id' => $legajoDetalle->legajo->matricula->categoria->tipo_categoria->id,
                            'nombre' => @$legajoDetalle->legajo->matricula->categoria->tipo_categoria->nombre,
                        ]
                    ],
                ] : null,
                'rubro' => isset($legajoDetalle->legajo->rubro) ? (object) [
                    'id' => $legajoDetalle->legajo->rubro->id,
                    'nombre' => $legajoDetalle->legajo->rubro->nombre,
                ] : null,
                'distrito' => $distrito,
                'localidad' => isset($legajoDetalle->legajo->inmuebles) ? (object) [
                    'id' => $legajoDetalle->legajo->inmuebles[0]->localidad->id,
                    'nombre' => $legajoDetalle->legajo->inmuebles[0]->localidad->nombre,
                    'partido' => (object) [
                        'id' => $legajoDetalle->legajo->inmuebles[0]->localidad->partido->id,
                        'nombre' => $legajoDetalle->legajo->inmuebles[0]->localidad->partido->nombre,
                    ],
                ] : null,
                'inmuebles' => isset($legajoDetalle->legajo->inmuebles) ? $legajoDetalle->legajo->inmuebles : null,
                'comitentes' => $comitentes,
            ],
        ];
    }

    private function getDataPersona($persona)
    {
        return (object) [
            'id' => $persona->id,
            'nombre' => $persona->nombre,
            'apellido' => $persona->apellido,
            'documento' => $persona->documento,
            'cuit' => $persona->cuit,
            'distrito' => $persona->distrito,
        ];
    }

    private function getDataConcepto($concepto)
    {
        return (object) [
            'id' => $concepto->id,
            'nombre' => $concepto->nombre,
            'key' => $concepto->key,
            'signo' => $concepto->signo,
            'tipo_item' => (object) [
                'id' => $concepto->tipo_item->id,
                'nombre' => $concepto->tipo_item->nombre,
                'key' => $concepto->tipo_item->key,
                'tipo' => $concepto->tipo_item->tipo,
            ]
        ];
    }

    public function generarConceptoOtro($item)
    {    
        $concepto = $this->httpService->findConceptoByKey($item->concepto->key)[0];

        $configuracionMonto = $concepto->gestion_monto->configuracion;
        $valor = isset($concepto->gestion_monto->valor) ? $concepto->gestion_monto->valor : null; 

        $importe_total = $item->importe_total;

        if ($configuracionMonto == 'manual_fijo') $importe_total = $valor;

        $item = (object) [
            'id' => Uuid::v4()->__toString(),
            'descripcion' => isset($item->descripcion) ? $item->descripcion : $concepto->nombre,
            'importe_total' => $importe_total,
            'fecha' => isset($item->fecha) ? $item->fecha : null,
            'concepto' => (object)[
                'id' => $concepto->id ,
                'nombre' => $concepto->nombre,
                'key' => $concepto->key,
                'signo' => $concepto->signo,
                'gestion_monto' => (object)[
                    'configuracion' => $configuracionMonto,
                    'valor' => $valor,
                ],
                'tipo_item' => (object)[
                   'id' => $concepto->tipo_item->id,
                   'nombre' => $concepto->tipo_item->nombre,
                   'key' => $concepto->tipo_item->key,
                   'tipo' => $concepto->tipo_item->tipo     
                ]
            ],
        ];

        return $item;
    }

}




    
/*
    * Recibe los valores necesarios y crea un objeto de tipo EpTimbradoInterno
    */
/*
/*
public function crearTimbradoInterno($ficha_detalle, $sede_activa, $alicuota_aplicada, $alicuota_calculada, $comision_aplicada, $comision_calculada, $mismoPartido, $date = null){
    $timbrado = new EpTimbradoInterno();
    $timbrado->setAlicuota($alicuota_aplicada); 
    $timbrado->setComision($comision_aplicada);
    $timbrado->setImporte(ceil($alicuota_calculada+$comision_calculada));
    $timbrado->setMismoPartido($mismoPartido);
    $this->asignarSedeTimbradoInterno($sede_activa, $timbrado);
    //campos heredados de concepto y requeridos
    if ($date == null)
        $date = new \DateTime('NOW');
    $timbrado->setFecha($date);
    $timbrado->setPersona($ficha_detalle->getFicha()->getPersona());
    $timbrado->setMatricula($ficha_detalle->getFicha()->getMatricula());
    $timbrado->setDescripcion('TIMBRADO FICHA #'.$ficha_detalle->getId());
    $timbrado->setFichaDetalle($ficha_detalle);
    $timbrado->setEstado($this->em->getRepository('DefaultBundle:CtaEstado')->findOneById(CtaEstado::PENDIENTE));
    $timbrado->setCategoria($this->em->getRepository('DefaultBundle:CtaCategoria')->findOneById(CtaCategoria::TIMBRADO));
    return $timbrado;        
}

public function asignarSedeTimbradoInterno($sede_activa, EpTimbradoInterno &$timbrado) 
{
    if (!empty($sede_activa)) {
        $timbrado->setSede($sede_activa);
        // lo agrego al lote abierto para esa sede
        $lote_timbrado_abierto = $this->em->getRepository('DefaultBundle:EpLoteTimbrado')->getLoteAbiertoForSede($sede_activa->getId());
        if ($lote_timbrado_abierto){
            $timbrado->setLote($lote_timbrado_abierto);
            // Si el timbrado tiene un acreditador asociado previamente (prepago), se asigna al proceso el mismo lote que el timbrado
            if(!empty($timbrado->getAcreditador()) && !empty($timbrado->getAcreditador()->getProcesos())) {
                foreach ($timbrado->getAcreditador()->getProcesos() as $proceso) {
                    $proceso->setLoteTimbrado($lote_timbrado_abierto);
                }
            }
        }
    }
}
*/