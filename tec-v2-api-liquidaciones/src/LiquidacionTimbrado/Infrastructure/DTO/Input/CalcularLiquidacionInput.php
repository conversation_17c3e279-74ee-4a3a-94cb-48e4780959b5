<?php

namespace App\LiquidacionTimbrado\Infrastructure\DTO\Input;

use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Serializer\Annotation\Groups;
use App\LiquidacionTimbrado\Infrastructure\DTO\Input\Item\ItemDTOInput;
use App\LiquidacionTimbrado\Infrastructure\DTO\Input\NotaCredito\NotaCreditoDTOInput;
use App\LiquidacionTimbrado\Infrastructure\DTO\Input\Pago\PagoDTOInput;
use App\LiquidacionTimbrado\Infrastructure\DTO\Input\PreferenciaPago\PreferenciaPagoDTOInput;
use App\Shared\Domain\ValueObject\Circuitos;
use App\Shared\Domain\ValueObject\Modo;

final class CalcularLiquidacionInput {

    #[Groups(['item:calculo:input','item:confirmar:input','item:recibo-cupon:input'])]
    private ?string $personaId = null;

    #[Groups(['item:calculo:input','item:confirmar:input','item:recibo-cupon:input'])]
    private ?string $cuit = null;

    #[Groups(['item:calculo:input','item:confirmar:input','item:recibo-cupon:input'])]
    private ?string $sedeId = null;

    /** @var ?ItemDTOInput[] $items */
    #[Groups(['item:calculo:input','item:confirmar:input','item:recibo-cupon:input'])]
    private $items = null;

    /** @var ?PreferenciaPagoDTOInput[] $preferencias */
    #[Groups(['item:calculo:input','item:confirmar:input','item:recibo-cupon:input'])]
    private $preferenciasPagos = null ;

    /** @var ?NotaCreditoDTOInput[] $notasCredito */
    #[Groups(['item:recibo-cupon:input'])]
    private $notasCredito = null ;
    
    /** @var ?PagoDTOInput[] $pagos */
    #[Groups(['item:recibo-cupon:input'])]
    private $pagos = null ;

    #[Groups(['item:confirmar:input','item:recibo-cupon:input'])]
    private \DateTime $fechaCalculo ;

    #[Groups(['item:recibo-cupon:input'])]
    private string $modo = Modo::CUPON;

    #[Groups(['item:recibo-cupon:input'])]
    private bool $generar = false;

    #[Groups(['item:recibo-cupon:input'])]
    private string $circuito = Circuitos::TIMBRADOS;

    #[Groups(['item:recibo-cupon:input'])]
    private bool $manual = false;

    public function __construct(){
        //$this->conceptos = new ArrayCollection() ;
        //$this->pagos = new ArrayCollection();
        //$this->notasCredito = new ArrayCollection();
        //$this->items =  new ArrayCollection();
        //$this->preferenciasPagos = new ArrayCollection();
    }

    public function getItems()
    {
        return $this->items;
    }

    public function setItems($items)
    {
        $this->items = $items;

        return $this;
    }

    public function getPersonaId()
    {
        return $this->personaId;
    }

    public function setPersonaId($personaId)
    {
        $this->personaId = $personaId;

        return $this;
    }

    /**
     * Get the value of pagos
     */ 
    public function getPagos()
    {
        return $this->pagos;
    }

    /**
     * Set the value of pagos
     *
     * @return  self
     */ 
    public function setPagos($pagos)
    {
        $this->pagos = $pagos;

        return $this;
    }

    /**
     * Get the value of preferenciasPagos
     */ 
    public function getPreferenciasPagos()
    {
        return $this->preferenciasPagos;
    }

    /**
     * Set the value of preferenciasPagos
     *
     * @return  self
     */ 
    public function setPreferenciasPagos($preferenciasPagos)
    {
        $this->preferenciasPagos = $preferenciasPagos;

        return $this;
    }

    /**
     * Get the value of notasCredito
     */ 
    public function getNotasCredito()
    {
        return $this->notasCredito;
    }

    /**
     * Set the value of notasCredito
     *
     * @return  self
     */ 
    public function setNotasCredito($notasCredito)
    {
        $this->notasCredito = $notasCredito;

        return $this;
    }

    /**
     * Get the value of fechaCalculo
     */ 
    public function getFechaCalculo()
    {
        return $this->fechaCalculo;
    }

    /**
     * Set the value of fechaCalculo
     *
     * @return  self
     */ 
    public function setFechaCalculo($fechaCalculo)
    {
        $this->fechaCalculo = $fechaCalculo;

        return $this;
    }

    /**
     * Get the value of modo
     */ 
    public function getModo()
    {
        return $this->modo;
    }

    /**
     * Set the value of modo
     *
     * @return  self
     */ 
    public function setModo($modo)
    {
        $this->modo = $modo;

        return $this;
    }

    /**
     * Get the value of generar
     */ 
    public function getGenerar()
    {
        return $this->generar;
    }

    /**
     * Set the value of generar
     *
     * @return  self
     */ 
    public function setGenerar($generar)
    {
        $this->generar = $generar;

        return $this;
    }

    /**
     * Get the value of circuito
     */ 
    public function getCircuito()
    {
        return $this->circuito;
    }

    /**
     * Set the value of circuito
     *
     * @return  self
     */ 
    public function setCircuito($circuito)
    {
        $this->circuito = $circuito;

        return $this;
    }

    /**
     * Get the value of sedeId
     */ 
    public function getSedeId()
    {
        return $this->sedeId;
    }

    /**
     * Set the value of sedeId
     *
     * @return  self
     */ 
    public function setSedeId($sedeId)
    {
        $this->sedeId = $sedeId;

        return $this;
    }

    /**
     * Get the value of cuit
     */ 
    public function getCuit()
    {
        return $this->cuit;
    }

    /**
     * Set the value of cuit
     *
     * @return  self
     */ 
    public function setCuit($cuit)
    {
        $this->cuit = $cuit;

        return $this;
    }

    /**
     * Get the value of manual
     */ 
    public function getManual()
    {
        return $this->manual;
    }

    /**
     * Set the value of manual
     *
     * @return  self
     */ 
    public function setManual($manual)
    {
        $this->manual = $manual;

        return $this;
    }
}