<?php

namespace App\LiquidacionTimbrado\Infrastructure\ApiPlatform\State\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\Validator\ValidatorInterface;
use App\Core\Application\Command\CommandBusInterface;
use App\Core\Infrastructure\ApiPlatform\State\GenericAuditableProcessor;
use App\Core\Infrastructure\ApiPlatform\State\GenericProcessor;
use App\LiquidacionTimbrado\Application\Command\GenerarCuponReciboManualCommand;
use App\LiquidacionTimbrado\Infrastructure\DTO\Output\Banco\BancoDTOOutput;
use App\LiquidacionTimbrado\Infrastructure\DTO\Output\Concepto\TipoItemDTOOutput;
use App\LiquidacionTimbrado\Infrastructure\DTO\Output\Cuenta\CuentaDTOOutput;
use App\LiquidacionTimbrado\Infrastructure\DTO\Output\GenerarCuponReciboDTOOutput;
use App\LiquidacionTimbrado\Infrastructure\DTO\Output\Instrumento\InstrumentoDTOOutput;
use App\LiquidacionTimbrado\Infrastructure\DTO\Output\NotaCredito\NotaCreditoDTOOutput;
use App\LiquidacionTimbrado\Infrastructure\DTO\Output\Pago\PagoDTOOutput;
use App\LiquidacionTimbrado\Infrastructure\Service\InfrastructureApplicationService;
use App\Shared\Application\Service\SharedHttpApplicationService;
use Symfony\Component\HttpFoundation\JsonResponse;

final class GenerarCuponReciboProcessor extends GenericAuditableProcessor
{
    const TIPO_OTRO_CONCEPTO = "otro_concepto";
    const TIPO_BONIFICACION_MANUAL = "bonificacion_manual";
    private $data;

    public function __construct(
        private CommandBusInterface $commandBus,
        private ValidatorInterface $validator,
        private InfrastructureApplicationService $infraApplicationService,
        private SharedHttpApplicationService $httpService 
    ) {
        parent::__construct($commandBus, $validator);
    }

    public function setCommand($data, Operation $operation, array $uriVariables = [], array $context = []): mixed
    {
        $this->data = $data;
        $items = $this->infraApplicationService->itemDTOtoArray($data);
        $pagos = $this->infraApplicationService->pagosDTOtoArray($data);
        $notas_creditos = $this->infraApplicationService->notasDTOtoArray($data);
        $preferencia_pagos = $this->infraApplicationService->preferenciasDTOtoArray($data);

        $command = new GenerarCuponReciboManualCommand(
            $data->getPersonaId(),
            $data->getSedeId(),
            $items,
            $pagos,
            $notas_creditos,
            $data->getFechaCalculo(),
            $data->getModo(),
            $data->getGenerar(),
            $data->getCircuito(),
            $preferencia_pagos,
            $data->getCuit(),
            $data->getManual()
        );

        return $command;
    }

    public function getResource($model)
    {
        if (!$this->data->getGenerar()) {
            $output = $this->convertirOutput(
                $model['liquidacion'],
                $model['notas_credito'],
                $model['pagos'],
                $model['fecha_calculo'],
                $model['monto_total'],
                //$model['preferencia_pagos']
            );
        } else {
            $output = new GenerarCuponReciboDTOOutput();
            $output->setMessage("Generado correctamente");
            $output->setLiquidacionId($model["liquidacion"]->getId()->getValue());
            $output->setOrdenPagoId($model["liquidacion"]->getOrdenPagoId() ? $model["liquidacion"]->getOrdenPagoId()->getValue() : null);
            //$output->setReciboId($model["liquidacion"]->getReciboId()->getValue());
        }

        return $output ;
    }

    public function convertirOutput($liquidacion,$notas_credito,$pagos,$fecha_calculo,$monto_total){
        
        $output = new GenerarCuponReciboDTOOutput();

        $items = [];
        $monto_conceptos = 0;
        foreach($liquidacion['items'] as $item){ 

            //$matricula = $this->httpService->obtenerMatricula($item['matricula']);
            $concepto = $this->httpService->obtenerConcepto($item['concepto']['id']);
            
            $monto_conceptos += $item['importe_total'] * $item['concepto']['signo'];

            // TODO: Que comprueba esto? [posiblemente sea para front] -> en todo caso pasarlo a la funcion convertItemToOutput()
            /*
            if($concepto->tipo_item->key == self::TIPO_OTRO_CONCEPTO || $concepto->key == self::TIPO_BONIFICACION_MANUAL ){
                $id = null ;
            }else{
                $id = $item['id'] ;
            }
            */
            
            $items[] = $this->infraApplicationService->convertItemToOutput($item);
        }

        $notasCredito = [];
        $monto_notas = 0;
        foreach($notas_credito as $nc){
            $monto_notas += $nc['importe'];
            $notasCredito[] = new NotaCreditoDTOOutput(
                $nc['id'],
                $nc['fecha'],
                $nc['descripcion'],
                $nc['importe']
            );
        }
 
        $pagosDTO = [];
        $monto_pagos = 0;
        foreach($pagos as $pago){
            $monto_pagos += $pago->importe_total; 
            $pagosDTO[] = new PagoDTOOutput(
                $pago->descripcion,
                new InstrumentoDTOOutput(
                    $pago->instrumento['id'],
                    $pago->instrumento['nombre'],
                    $pago->instrumento['key'],
                    $pago->instrumento['signo'], 
                    new TipoItemDTOOutput(
                        $pago->instrumento['tipo_item']['id'],
                        $pago->instrumento['tipo_item']['nombre'],
                        $pago->instrumento['tipo_item']['key'],
                        $pago->instrumento['tipo_item']['tipo']
                    )
                ),
                \DateTime::createFromFormat('Y-m-d H:i:s', $pago->fecha_pago),
                $pago->cuenta ? new CuentaDTOOutput(
                    $pago->cuenta['id'],
                    $pago->cuenta['nombre'],
                    $pago->cuenta['numero'],
                    $pago->cuenta['sucursal'],
                    new BancoDTOOutput(
                        $pago->cuenta['banco']['id'],
                        $pago->cuenta['banco']['nombre'],
                    )
                ) : null,
                $pago->transaccion,
                $pago->operacion,
                $pago->importe_total
            );
        }

        $flags = $this->infraApplicationService->calculoFlagValidacion($monto_total,!empty($pagos));

        $output->setConceptos($items);
        //$output->setSede($sede);
        $output->setNotasCredito($notasCredito);
        $output->setPagos($pagosDTO);
        $output->setMontoConceptos($monto_conceptos - $monto_notas);
        $output->setMontoPagos($monto_pagos); 
        $output->setMontoTotal($monto_total);
        $output->setRecibo($flags['recibo']);
        $output->setCupon($flags['cupon']);
        $output->setNotaCredito($flags['notaCredito']);
        $output->setFechaCalculo($fecha_calculo);

        return $output ;
        
    }
}