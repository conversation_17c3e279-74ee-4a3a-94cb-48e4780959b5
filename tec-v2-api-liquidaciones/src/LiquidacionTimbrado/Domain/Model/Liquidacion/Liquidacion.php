<?php

declare(strict_types=1);

namespace App\LiquidacionTimbrado\Domain\Model\Liquidacion;

use App\LiquidacionTimbrado\Domain\Model\Item\Item;
use App\LiquidacionTimbrado\Domain\Model\Item\ItemId;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;
use App\Shared\Domain\Model\Persona\Persona;
use App\LiquidacionTimbrado\Domain\Model\Liquidacion\DistritoId;
use App\Shared\Domain\Model\LiquidacionBase\LiquidacionId;
use App\Shared\Domain\Model\LiquidacionBase\FechaCalculo;
use App\Shared\Domain\Model\LiquidacionBase\FechaLiquidacion;
use App\Shared\Domain\Model\LiquidacionBase\LiquidacionBase;
use App\Shared\Domain\Model\LiquidacionBase\MontoTotal;
use App\Shared\Domain\Model\LiquidacionBase\OrdenPagoId;
use App\Shared\Domain\Model\LiquidacionBase\ValidoHasta;
use App\Shared\Domain\Model\Sede\Sede;
use App\Shared\Domain\ValueObject\Circuitos;

class Liquidacion extends LiquidacionBase
{   
    protected ?DistritoId $distrito;
    protected Collection $items;

    public function getType() {
        return Circuitos::TIMBRADOS;
    }

    public static function isAuditable(): bool
    {
        return true;
    }

    public function __construct(
        // Atributos propios
        DistritoId $distrito,
        // Atributos del parent
        LiquidacionId $id ,
        Persona $persona,
        FechaCalculo $fechaCalculo,
        FechaLiquidacion $fechaLiquidacion,
        ValidoHasta $validoHasta,
        ?OrdenPagoId $ordenPagoId,
        MontoTotal $montoTotal,
        ?Sede $sede = null,
        ?string $createdBy = null
    )
    {   
        parent::__construct(
            $id,
            $persona,
            $fechaCalculo,
            $fechaLiquidacion,
            $validoHasta,
            $ordenPagoId,
            $montoTotal,
            $sede,
        );

        $this->distrito = $distrito;

        $this->createdAt = new \DateTime('NOW');
        $this->createdBy = $createdBy;

        //Colecciones
        $this->items = new ArrayCollection();
    }
    
    // -------------------------- create Liquidacion -------------------------

    public static function create(
        LiquidacionId $id,
        Persona $persona,
        DistritoId $distrito,
        array $items,
        FechaCalculo $fechaCalculo,
        FechaLiquidacion $fechaLiquidacion,
        ValidoHasta $validoHasta,
        ?OrdenPagoId $ordenPagoId,
        MontoTotal $montoTotal,
        ?Sede $sede = null,
        ?string $createdBy
    ): Liquidacion
    {
        $liquidacion = new self (
            $distrito,
            $id,
            $persona,
            $fechaCalculo,
            $fechaLiquidacion,
            $validoHasta,
            $ordenPagoId,
            $montoTotal,
            $sede,
            $createdBy
        );

        
        foreach ($items as $item) {
            $it = new Item(
                $item['id'],
                $item['importeTotal'],
                $item['concepto'],
                @$item['identificador'],
                @$item['vencimiento'],
                @$item['descripcion'],
                @$item['alicuota'],
                @$item['montoAlicuota'],
                @$item['comision'],
                @$item['montoComision'],
                @$item['prepago'],
                @$item['mismoPartido'],
                @$item['objeto'],
                @$item['referencia'],
                @$item['fecha'],
                @$item['metadata'],
            );
            $liquidacion->addItem($it);
        }
        
        
        return $liquidacion ;
    }

    // -------------------------------------- Create Item -------------------------------

    /*
    public static function createItem(
        ItemId $id,
        Descripcion $descripcion,
        ItemMatriculaId $matricula ,
        Identificador $identificador,
        Concepto $concepto,
        PlanPagoId $planPagoId,
        Numero $numero,
        ImporteOriginal $importeOriginal,
        Ajuste $ajuste,
        Interes $interes,
        Vencimiento $vencimiento,
        ImporteTotal $importeTotal,
        ?int $idSistemaAnterior
    ):Item
    {
        $item = new Item(
            $id,
            $descripcion,
            $matricula,
            $identificador,
            $concepto,
            $planPagoId,
            $numero,
            $importeOriginal,
            $ajuste,
            $interes,
            $vencimiento,
            $importeTotal,
            $idSistemaAnterior
        );
        return $item ;
    }
    */

    public function update (
        ?Persona $persona = null,
        //?DistritoId $distrito = null, // Atributo propio ?
        ?array $items = null,
        ?FechaCalculo $fechaCalculo = null,
        ?FechaLiquidacion $fechaLiquidacion = null,
        ?ValidoHasta $validoHasta = null,
        ?OrdenPagoId $ordenPagoId = null ,
        ?MontoTotal $montoTotal  = null
    ):void
    {
        $this->persona = $persona ?? $this->$persona;
        //$this->distrito = $distrito ?? $this->distrito ;
        $this->fechaCalculo = $fechaCalculo ?? $this->fechaCalculo;
        $this->fechaLiquidacion = $fechaLiquidacion ?? $this->fechaLiquidacion;
        $this->validoHasta = $validoHasta ?? $this->validoHasta ;
        $this->ordenPagoId = $ordenPagoId ?? $this->ordenPagoId;
        $this->montoTotal = $montoTotal ?? $this->montoTotal ;

        /*
        if($items != null){
            $this->items = new ArrayCollection();
            foreach ($items as $item) {
                $it = self::createItem(
                    $item['id'],
                    $item['descripcion'],
                    $item['matricula'],
                    $item['identificador'],
                    $item['concepto'],
                    $item['planPagoId'],
                    $item['numero'],
                    $item['importeOriginal'],
                    $item['ajuste'],
                    $item['interes'],
                    $item['vencimiento'],
                    $item['importeTotal'],
                    $item['idSistemaAnterior']
                );
                $this->addItem($it);
            }
        }
        */
       
    }


    /**
     * Get the value of id
     */ 
    public function getId(): LiquidacionId
    {
        return new LiquidacionId($this->id);
    }

    /**
     * Get the value of persona
     */ 
    public function getPersona()
    {
        return $this->persona;
    }

    /**
     * Set the value of persona
     *
     * @return  self
     */ 
    public function setPersona($persona):self
    {
        $this->persona = $persona;

        return $this;
    }

    /**
     * Get the value of distrito
     */ 
    public function getDistrito()
    {
        return $this->distrito;
    }

    /**
     * Set the value of distrito
     *
     * @return  self
     */ 
    public function setDistrito($distrito):self
    {
        $this->distrito = $distrito;

        return $this;
    }

    /**
     * Get the value of items
     */ 
    public function getItems(): Collection
    {
        return $this->items;
    }

    /**
     * Set the value of items
     *
     * @return  self
     */ 
    public function setItems(Collection $items): self
    {
        $this->items = $items;

        return $this;
    }


    public function addItem(Item $item): self
    {
        if (!$this->items->contains($item)) {
            $this->items[] = $item;
        }

        return $this;
    }

    /**
     * Get the value of fechaCalculo
     */ 
    public function getFechaCalculo()
    {
        return $this->fechaCalculo;
    }

    /**
     * Set the value of fechaCalculo
     *
     * @return  self
     */ 
    public function setFechaCalculo($fechaCalculo)
    {
        $this->fechaCalculo = $fechaCalculo;

        return $this;
    }

    /**
     * Get the value of fechaLiquidacion
     */ 
    public function getFechaLiquidacion()
    {
        return $this->fechaLiquidacion;
    }

    /**
     * Set the value of fechaLiquidacion
     *
     * @return  self
     */ 
    public function setFechaLiquidacion($fechaLiquidacion)
    {
        $this->fechaLiquidacion = $fechaLiquidacion;

        return $this;
    }

    /**
     * Get the value of validoHasta
     */ 
    public function getValidoHasta()
    {
        return $this->validoHasta;
    }

    /**
     * Set the value of validoHasta
     *
     * @return  self
     */ 
    public function setValidoHasta($validoHasta)
    {
        $this->validoHasta = $validoHasta;

        return $this;
    }

    /**
     * Get the value of montoTotal
     */ 
    public function getMontoTotal()
    {
        return $this->montoTotal;
    }

    /**
     * Set the value of montoTotal
     *
     * @return  self
     */ 
    public function setMontoTotal($montoTotal)
    {
        $this->montoTotal = $montoTotal;

        return $this;
    }

    /**
     * Get the value of ordenPagoId
     */ 
    public function getOrdenPagoId()
    {
        return $this->ordenPagoId;
    }

    /**
     * Set the value of ordenPagoId
     *
     * @return  self
     */ 
    public function setOrdenPagoId($ordenPagoId)
    {
        $this->ordenPagoId = $ordenPagoId;

        return $this;
    }

    /**
     * Get the value of createdAt
     */ 
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set the value of createdAt
     *
     * @return  self
     */ 
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get the value of updatedAt
     */ 
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Set the value of updatedAt
     *
     * @return  self
     */ 
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get the value of createdBy
     */ 
    public function getCreatedBy()
    {
        return $this->createdBy;
    }

    /**
     * Set the value of createdBy
     *
     * @return  self
     */ 
    public function setCreatedBy($createdBy)
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    /**
     * Get the value of updatedBy
     */ 
    public function getUpdatedBy()
    {
        return $this->updatedBy;
    }

    /**
     * Set the value of updatedBy
     *
     * @return  self
     */ 
    public function setUpdatedBy($updatedBy)
    {
        $this->updatedBy = $updatedBy;

        return $this;
    }

    /**
     * Get the value of deletedAt
     */ 
    public function getDeletedAt()
    {
        return $this->deletedAt;
    }

    /**
     * Set the value of deletedAt
     *
     * @return  self
     */ 
    public function setDeletedAt($deletedAt)
    {
        $this->deletedAt = $deletedAt;

        return $this;
    }
}