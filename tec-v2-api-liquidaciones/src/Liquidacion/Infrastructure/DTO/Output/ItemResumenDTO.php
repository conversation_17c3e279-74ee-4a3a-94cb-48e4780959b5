<?php

namespace App\Liquidacion\Infrastructure\DTO\Output;

use DateTime;
use Symfony\Component\Serializer\Annotation\Groups;


final class ItemResumenDTO {

    #[Groups(['resumen:output'])]
    private ?string $codigoMatricula ;

    #[Groups(['resumen:output'])]
    private string $estado ;

    #[Groups(['resumen:output'])]
    private DateTime $fechaVencimiento ;

    #[Groups(['resumen:output'])]
    private string $descripcion ;

    #[Groups(['resumen:output'])]
    private float $importeTotal ;

    #[Groups(['resumen:output'])]
    private ?string $reciboId ;

    #[Groups(['resumen:output'])]
    private ?DateTime $fechaPago ; 

    #[Groups(['resumen:output'])]
    private ?string $ordenPagoId ;

    #[Groups(['resumen:output'])]
    private ?string $type ;

    public function __construct(
        ?string $codigoMatricula,
        string $estado,
        DateTime $fechaVencimiento,
        string $descripcion,
        float $importeTotal,
        ?string $reciboId = null,
        ?DateTime $fechaPago = null,
        ?string $ordenPagoId = null,
        ?string $type = null
    ){
        $this->codigoMatricula = $codigoMatricula;
        $this->estado = $estado ;
        $this->fechaVencimiento = $fechaVencimiento ;
        $this->descripcion = $descripcion;
        $this->importeTotal = $importeTotal ;
        $this->reciboId = $reciboId;
        $this->fechaPago = $fechaPago;
        $this->ordenPagoId = $ordenPagoId;
        $this->type = $type ;
    }


    /**
     * Get the value of codigoMatricula
     */ 
    public function getCodigoMatricula()
    {
        return $this->codigoMatricula;
    }

    /**
     * Set the value of codigoMatricula
     *
     * @return  self
     */ 
    public function setCodigoMatricula($codigoMatricula)
    {
        $this->codigoMatricula = $codigoMatricula;

        return $this;
    }

    /**
     * Get the value of estado
     */ 
    public function getEstado()
    {
        return $this->estado;
    }

    /**
     * Set the value of estado
     *
     * @return  self
     */ 
    public function setEstado($estado)
    {
        $this->estado = $estado;

        return $this;
    }

    /**
     * Get the value of fechaVencimiento
     */ 
    public function getFechaVencimiento()
    {
        return $this->fechaVencimiento;
    }

    /**
     * Set the value of fechaVencimiento
     *
     * @return  self
     */ 
    public function setFechaVencimiento($fechaVencimiento)
    {
        $this->fechaVencimiento = $fechaVencimiento;

        return $this;
    }

    /**
     * Get the value of descripcion
     */ 
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * Set the value of descripcion
     *
     * @return  self
     */ 
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;

        return $this;
    }

    /**
     * Get the value of importeTotal
     */ 
    public function getImporteTotal()
    {
        return $this->importeTotal;
    }

    /**
     * Set the value of importeTotal
     *
     * @return  self
     */ 
    public function setImporteTotal($importeTotal)
    {
        $this->importeTotal = $importeTotal;

        return $this;
    }

    /**
     * Get the value of reciboId
     */ 
    public function getReciboId()
    {
        return $this->reciboId;
    }

    /**
     * Set the value of reciboId
     *
     * @return  self
     */ 
    public function setReciboId($reciboId)
    {
        $this->reciboId = $reciboId;

        return $this;
    }

    /**
     * Get the value of fechaPago
     */ 
    public function getFechaPago()
    {
        return $this->fechaPago;
    }

    /**
     * Set the value of fechaPago
     *
     * @return  self
     */ 
    public function setFechaPago($fechaPago)
    {
        $this->fechaPago = $fechaPago;

        return $this;
    }

    /**
     * Get the value of ordenPagoId
     */ 
    public function getOrdenPagoId()
    {
        return $this->ordenPagoId;
    }

    /**
     * Set the value of ordenPagoId
     *
     * @return  self
     */ 
    public function setOrdenPagoId($ordenPagoId)
    {
        $this->ordenPagoId = $ordenPagoId;

        return $this;
    }

    /**
     * Get the value of type
     */ 
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set the value of type
     *
     * @return  self
     */ 
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }
}
