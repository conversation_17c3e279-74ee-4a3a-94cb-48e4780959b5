<?php

namespace App\Liquidacion\Infrastructure\DTO\Output;

use DateTime;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use App\Liquidacion\Infrastructure\DTO\Input\Concepto\ConceptoDTO;
use App\Liquidacion\Infrastructure\DTO\Input\Matricula\MatriculaDTO;

// FIX: Esto no es lo mismo que item ItemDTO? (App\Liquidacion\Infrastructure\DTO\Input\Item\ItemDTO)
final class ConceptoOutputDTO {

    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])]
    private ?string $id ;


    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])]
    private MatriculaDTO $matricula ;

    
    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])]
    private string $descripcion ;

    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])]
    private ConceptoDTO $concepto ;

    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])]    
    private string $planPagoId ;

    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])] 
    private int $numero ;
   
    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])]
    private float $importeOriginal ;
    
    
    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])]
    private float $ajuste ;

  
    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])]
    private float $interes ;


    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])]
    private DateTime $vencimiento;


    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])]
    private float $importeTotal ;

    #[Assert\NotNull(groups: [])]
    #[Groups(['item:calculo:output','item:calculo:input','item:confirmar:input','item:recibo-cupon:output'])]
    private ?DateTime $fecha ;
    
    public function __construct(
        ?string $id,
        ?MatriculaDTO $matricula,
        ?string $descripcion,
        ?conceptoDTO $concepto,
        ?string $planPagoId,
        ?int $numero,
        ?float $importeOriginal,
        ?float $ajuste,
        ?float $interes,
        ?DateTime $vencimiento,
        ?DateTime $fecha,
        ?float $importeTotal
    ) {
        $this->id = $id ?? null ;  
        $this->matricula = $matricula ?? null;
        $this->descripcion = $descripcion ?? null;
        $this->concepto = $concepto ?? null ;
        $this->planPagoId = $planPagoId ?? null ;
        $this->numero = $numero ?? 0 ;
        $this->importeOriginal = $importeOriginal ?? 0;
        $this->ajuste = $ajuste ?? 0 ;
        $this->interes = $interes ?? 0;
        $this->vencimiento = $vencimiento ?? new DateTime("now");
        $this->fecha = $fecha ?? null;
        $this->importeTotal = $importeTotal ?? 0 ;

    }


    /**
     * Get the value of descripcion
     */ 
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * Set the value of descripcion
     *
     * @return  self
     */ 
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;

        return $this;
    }

  
    /**
     * Get the value of importeOriginal
     */ 
    public function getImporteOriginal()
    {
        return $this->importeOriginal;
    }

    /**
     * Set the value of importeOriginal
     *
     * @return  self
     */ 
    public function setImporteOriginal($importeOriginal)
    {
        $this->importeOriginal = $importeOriginal;

        return $this;
    }

    /**
     * Get the value of ajuste
     */ 
    public function getAjuste()
    {
        return $this->ajuste;
    }

    /**
     * Set the value of ajuste
     *
     * @return  self
     */ 
    public function setAjuste($ajuste)
    {
        $this->ajuste = $ajuste;

        return $this;
    }

    /**
     * Get the value of interes
     */ 
    public function getInteres()
    {
        return $this->interes;
    }

    /**
     * Set the value of interes
     *
     * @return  self
     */ 
    public function setInteres($interes)
    {
        $this->interes = $interes;

        return $this;
    }

    /**
     * Get the value of vencimiento
     */ 
    public function getVencimiento()
    {
        return $this->vencimiento;
    }

    /**
     * Set the value of vencimiento
     *
     * @return  self
     */ 
    public function setVencimiento($vencimiento)
    {
        $this->vencimiento = $vencimiento;

        return $this;
    }

    /**
     * Get the value of id
     */ 
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set the value of id
     *
     * @return  self
     */ 
    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Get the value of concepto
     */ 
    public function getConcepto()
    {
        return $this->concepto;
    }

    /**
     * Set the value of concepto
     *
     * @return  self
     */ 
    public function setConcepto($concepto)
    {
        $this->concepto = $concepto;

        return $this;
    }

    /**
     * Get the value of planPagoId
     */ 
    public function getPlanPagoId()
    {
        return $this->planPagoId;
    }

    /**
     * Set the value of planPagoId
     *
     * @return  self
     */ 
    public function setPlanPagoId($planPagoId)
    {
        $this->planPagoId = $planPagoId;

        return $this;
    }

    /**
     * Get the value of numero
     */ 
    public function getNumero()
    {
        return $this->numero;
    }

    /**
     * Set the value of numero
     *
     * @return  self
     */ 
    public function setNumero($numero)
    {
        $this->numero = $numero;

        return $this;
    }

    /**
     * Get the value of importeTotal
     */ 
    public function getImporteTotal()
    {
        return $this->importeTotal;
    }

    /**
     * Set the value of importeTotal
     *
     * @return  self
     */ 
    public function setImporteTotal($importeTotal)
    {
        $this->importeTotal = $importeTotal;

        return $this;
    }

    /**
     * Get the value of matricula
     */ 
    public function getMatricula()
    {
        return $this->matricula;
    }

    /**
     * Set the value of matricula
     *
     * @return  self
     */ 
    public function setMatricula($matricula)
    {
        $this->matricula = $matricula;

        return $this;
    }

    /**
     * Get the value of fecha
     */ 
    public function getFecha()
    {
        return $this->fecha;
    }

    /**
     * Set the value of fecha
     *
     * @return  self
     */ 
    public function setFecha($fecha)
    {
        $this->fecha = $fecha;

        return $this;
    }
}
