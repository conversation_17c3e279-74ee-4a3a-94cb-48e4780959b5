<?php

namespace App\Liquidacion\Infrastructure\DTO\Output;

use App\Core\Infrastructure\ApiPlatform\Resource\ResourceAuditableInterface;
use App\Liquidacion\Domain\Model\Liquidacion\Liquidacion;
use Symfony\Component\Serializer\Annotation\Groups;


final class ValidarPagoOutput implements ResourceAuditableInterface {

    public function getId()
    {
        return $this->getReciboId() ?? '';
    }

    public static function isAuditable(): bool
    {
        return Liquidacion::isAuditable();
    }

    public static function getModel(): string 
    {
        return Liquidacion::class;
    }

    #[Groups(['operacionPago:validar:output'])]
    private bool $valido ;

    #[Groups(['operacionPago:validar:output'])]
    private string $message;

    #[Groups(['operacionPago:validar:output'])]
    private bool $notaCredito;

    #[Groups(['operacionPago:validar:output'])]
    private float $montoNota;

    #[Groups(['operacionPago:validar:output'])]
    private string $reciboId;

    #[Groups(['operacionPago:validar:output'])]
    private string $numeroRecibo;
    
   #[Groups(['operacionPago:validar:output'])]
    private bool $itemsValidos;

    public function __construct(){}


    /**
     * Get the value of valido
     */ 
    public function getValido()
    {
        return $this->valido;
    }

    /**
     * Set the value of valido
     *
     * @return  self
     */ 
    public function setValido($valido)
    {
        $this->valido = $valido;

        return $this;
    }

    /**
     * Get the value of message
     */ 
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * Set the value of message
     *
     * @return  self
     */ 
    public function setMessage($message)
    {
        $this->message = $message;

        return $this;
    }

    /**
     * Get the value of notaCredito
     */ 
    public function getNotaCredito()
    {
        return $this->notaCredito;
    }

    /**
     * Set the value of notaCredito
     *
     * @return  self
     */ 
    public function setNotaCredito($notaCredito)
    {
        $this->notaCredito = $notaCredito;

        return $this;
    }

    /**
     * Get the value of montoNota
     */ 
    public function getMontoNota()
    {
        return $this->montoNota;
    }

    /**
     * Set the value of montoNota
     *
     * @return  self
     */ 
    public function setMontoNota($montoNota)
    {
        $this->montoNota = $montoNota;

        return $this;
    }

    /**
     * Get the value of reciboId
     */ 
    public function getReciboId()
    {
        return $this->reciboId;
    }

    /**
     * Set the value of reciboId
     *
     * @return  self
     */ 
    public function setReciboId($reciboId)
    {
        $this->reciboId = $reciboId;

        return $this;
    }

    /**
     * Get the value of numeroRecibo
     */ 
    public function getNumeroRecibo()
    {
        return $this->numeroRecibo;
    }

    /**
     * Set the value of numeroRecibo
     *
     * @return  self
     */ 
    public function setNumeroRecibo($numeroRecibo)
    {
        $this->numeroRecibo = $numeroRecibo;

        return $this;
    }

    /**
     * Get the value of itemsValidos
     */ 
    public function getItemsValidos()
    {
        return $this->itemsValidos;
    }

    /**
     * Set the value of itemsValidos
     *
     * @return  self
     */ 
    public function setItemsValidos($itemsValidos)
    {
        $this->itemsValidos = $itemsValidos;

        return $this;
    }
}
