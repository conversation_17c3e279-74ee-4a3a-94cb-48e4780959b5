<?php

namespace App\Liquidacion\Infrastructure\DTO\Input;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Serializer\Annotation\Groups;
use App\Liquidacion\Infrastructure\DTO\Input\Item\ItemDTO;
use App\Liquidacion\Infrastructure\DTO\Input\Pago\PagoDTO;
use Symfony\Component\Serializer\Annotation\SerializedName;
use App\Liquidacion\Infrastructure\DTO\Input\NotaCredito\NotaCreditoDTO;
use App\Liquidacion\Infrastructure\DTO\Input\PreferenciaPago\PreferenciaPagoDTO;

final class ItemInput {

    /** @var string $personaId */
    #[Groups(['item:calculo:input','item:confirmar:input','item:recibo-cupon:input'])]
    private string $personaId ;

    /** @var ?ItemDTO[] $conceptos */
    #[Groups(['item:recibo-cupon:input'])]
    private $conceptos = null ;

    //este solo se utiliza para liquidacion automatica
    /** @var ?ItemDTO[] $items */
    #[Groups(['item:calculo:input','item:confirmar:input'])]
    private $items = null ;

    //este solo se utiliza para liquidacion automatica 
    /** @var ?PreferenciaPagoDTO[] $preferencias */
    #[Groups(['item:calculo:input','item:confirmar:input'])]
    #[SerializedName('preferencias_de_pago')]   
    private $preferenciasPagos = null ;

    /** @var ?NotaCreditoDTO[] $notasCredito */
    #[Groups(['item:recibo-cupon:input'])]
    private $notasCredito = null ;
    
    /** @var ?PagoDTO[] $pagos */
    #[Groups(['item:recibo-cupon:input'])]
    private $pagos = null ;

    
    #[Groups(['item:confirmar:input','item:recibo-cupon:input'])]
    private DateTime $fechaCalculo ;

    #[Groups(['item:recibo-cupon:input'])]
    private string $modo ;

    #[Groups(['item:recibo-cupon:input'])]
    private bool $generar;

    #[Groups(['item:recibo-cupon:input'])]
    private string $circuito;

    #[Groups(['item:recibo-cupon:input'])]
    private bool $manual = false;

    public function __construct(){
        $this->conceptos = new ArrayCollection() ;
        $this->pagos = new ArrayCollection();
        $this->notasCredito = new ArrayCollection();
        $this->items =  new ArrayCollection();
        $this->preferenciasPagos = new ArrayCollection();
    }


   

    /**
     * Get the value of personaId
     */ 
    public function getPersonaId()
    {
        return $this->personaId;
    }

    /**
     * Set the value of personaId
     *
     * @return  self
     */ 
    public function setPersonaId($personaId)
    {
        $this->personaId = $personaId;

        return $this;
    }

    /**
     * Get the value of pagos
     */ 
    public function getPagos()
    {
        return $this->pagos;
    }

    /**
     * Set the value of pagos
     *
     * @return  self
     */ 
    public function setPagos($pagos)
    {
        $this->pagos = $pagos;

        return $this;
    }



    /**
     * Get the value of modo
     */ 
    public function getModo()
    {
        return $this->modo;
    }

    /**
     * Set the value of modo
     *
     * @return  self
     */ 
    public function setModo($modo)
    {
        $this->modo = $modo;

        return $this;
    }

    /**
     * Get the value of generar
     */ 
    public function getGenerar()
    {
        return $this->generar;
    }

    /**
     * Set the value of generar
     *
     * @return  self
     */ 
    public function setGenerar($generar)
    {
        $this->generar = $generar;

        return $this;
    }


    /**
     * Get the value of conceptos
     */ 
    public function getConceptos()
    {
        return $this->conceptos;
    }

    /**
     * Set the value of conceptos
     *
     * @return  self
     */ 
    public function setConceptos($conceptos)
    {
        $this->conceptos = $conceptos;

        return $this;
    }



    /**
     * Get the value of notasCredito
     */ 
    public function getNotasCredito()
    {
        return $this->notasCredito;
    }

    /**
     * Set the value of notasCredito
     *
     * @return  self
     */ 
    public function setNotasCredito($notasCredito)
    {
        $this->notasCredito = $notasCredito;

        return $this;
    }

    /**
     * Get the value of fechaCalculo
     */ 
    public function getFechaCalculo()
    {
        return $this->fechaCalculo;
    }

    /**
     * Set the value of fechaCalculo
     *
     * @return  self
     */ 
    public function setFechaCalculo($fechaCalculo)
    {
        $this->fechaCalculo = $fechaCalculo;

        return $this;
    }

    /**
     * Get the value of items
     */ 
    public function getItems()
    {
        return $this->items;
    }

    /**
     * Set the value of items
     *
     * @return  self
     */ 
    public function setItems($items)
    {
        $this->items = $items;

        return $this;
    }

    /**
     * Get the value of preferenciasPagos
     */ 
    public function getPreferenciasPagos()
    {
        return $this->preferenciasPagos;
    }

    /**
     * Set the value of preferenciasPagos
     *
     * @return  self
     */ 
    public function setPreferenciasPagos($preferenciasPagos)
    {
        $this->preferenciasPagos = $preferenciasPagos;

        return $this;
    }

    /**
     * Get the value of circuito
     */ 
    public function getCircuito()
    {
        return $this->circuito;
    }

    /**
     * Set the value of circuito
     *
     * @return  self
     */ 
    public function setCircuito($circuito)
    {
        $this->circuito = $circuito;

        return $this;
    }

    /**
     * Get the value of manual
     */ 
    public function getManual()
    {
        return $this->manual;
    }

    /**
     * Set the value of manual
     *
     * @return  self
     */ 
    public function setManual($manual)
    {
        $this->manual = $manual;

        return $this;
    }
}