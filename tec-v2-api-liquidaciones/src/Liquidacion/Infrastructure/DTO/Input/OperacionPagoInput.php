<?php

namespace App\Liquidacion\Infrastructure\DTO\Input;

use DateTime;
use Symfony\Component\Serializer\Annotation\Groups;
use App\Liquidacion\Infrastructure\DTO\Input\ItemOperacionPago\ItemOperacionPagoDTO;

final class OperacionPagoInput {


    #[Groups(['operacionPago:validar:input','item:recibo-cupon:input'])]
    private string $microservicio;
    
    #[Groups(['operacionPago:validar:input','item:recibo-cupon:input'])]
    private string $agente ;

    #[Groups(['operacionPago:validar:input','item:recibo-cupon:input'])]
    private float $montoTotal;

    /** @var ?ItemOperacionPagoDTO[] $items */
    #[Groups(['operacionPago:validar:input','item:recibo-cupon:input'])]
    private $items = null  ;

    #[Groups(['operacionPago:validar:input','item:recibo-cupon:input'])]
    private DateTime $fechaPago;

    #[Groups(['operacionPago:validar:input','item:recibo-cupon:input'])]
    private DateTime $fecha ;

    #[Groups(['operacionPago:validar:input','item:recibo-cupon:input'])]
    private ?string $ordenPagoId = null ;

    #[Groups(['operacionPago:validar:input','item:recibo-cupon:input'])]
    private ?string $recaudadorId = null ;

    #[Groups(['operacionPago:validar:input','item:recibo-cupon:input'])]
    private ?string $reciboId = null;

    public function __construct(){}
    /**
     * Get the value of id
     */ 

    /**
     * Get the value of microservicio
     */ 
    public function getMicroservicio()
    {
        return $this->microservicio;
    }

    /**
     * Set the value of microservicio
     *
     * @return  self
     */ 
    public function setMicroservicio($microservicio)
    {
        $this->microservicio = $microservicio;

        return $this;
    }

    /**
     * Get the value of agente
     */ 
    public function getAgente()
    {
        return $this->agente;
    }

    /**
     * Set the value of agente
     *
     * @return  self
     */ 
    public function setAgente($agente)
    {
        $this->agente = $agente;

        return $this;
    }

    /**
     * Get the value of montoTotal
     */ 
    public function getMontoTotal()
    {
        return $this->montoTotal;
    }

    /**
     * Set the value of montoTotal
     *
     * @return  self
     */ 
    public function setMontoTotal($montoTotal)
    {
        $this->montoTotal = $montoTotal;

        return $this;
    }

    /**
     * Get the value of items
     */ 
    public function getItems()
    {
        return $this->items;
    }

    /**
     * Set the value of items
     *
     * @return  self
     */ 
    public function setItems($items)
    {
        $this->items = $items;

        return $this;
    }

    /**
     * Get the value of fechaPago
     */ 
    public function getFechaPago()
    {
        return $this->fechaPago;
    }

    /**
     * Set the value of fechaPago
     *
     * @return  self
     */ 
    public function setFechaPago($fechaPago)
    {
        $this->fechaPago = $fechaPago;

        return $this;
    }

    /**
     * Get the value of fecha
     */ 
    public function getFecha()
    {
        return $this->fecha;
    }

    /**
     * Set the value of fecha
     *
     * @return  self
     */ 
    public function setFecha($fecha)
    {
        $this->fecha = $fecha;

        return $this;
    }

    /**
     * Get the value of ordenPagoId
     */ 
    public function getOrdenPagoId()
    {
        return $this->ordenPagoId;
    }

    /**
     * Set the value of ordenPagoId
     *
     * @return  self
     */ 
    public function setOrdenPagoId($ordenPagoId)
    {
        $this->ordenPagoId = $ordenPagoId;

        return $this;
    }

    /**
     * Get the value of recaudadorId
     */ 
    public function getRecaudadorId()
    {
        return $this->recaudadorId;
    }

    /**
     * Set the value of recaudadorId
     *
     * @return  self
     */ 
    public function setRecaudadorId($recaudadorId)
    {
        $this->recaudadorId = $recaudadorId;

        return $this;
    }

    /**
     * Get the value of reciboId
     */ 
    public function getReciboId()
    {
        return $this->reciboId;
    }

    /**
     * Set the value of reciboId
     *
     * @return  self
     */ 
    public function setReciboId($reciboId)
    {
        $this->reciboId = $reciboId;

        return $this;
    }
}