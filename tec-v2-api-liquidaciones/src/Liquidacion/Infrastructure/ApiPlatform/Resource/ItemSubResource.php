<?php

declare(strict_types=1);

namespace App\Liquidacion\Infrastructure\ApiPlatform\Resource;

use App\Liquidacion\Domain\Model\Item\Item;
use Symfony\Component\Serializer\Annotation\Groups;
use App\Liquidacion\Infrastructure\ApiPlatform\Resource\ConceptoSubResource;
use Symfony\Component\Validator\Constraints as Assert;

final class ItemSubResource
{
    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    private string $id ;

    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    #[Assert\NotNull(groups: ['liquidacion:post'])]
    #[Assert\NotBlank(groups: ['liquidacion:post'])]
    #[Assert\Length(min: 1, max: 250, groups: ['liquidacion:post', 'liquidacion:put'])]
    private string $descripcion ;
    
    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    #[Assert\NotNull(groups: ['liquidacion:post'])]
    #[Assert\NotBlank(groups: ['liquidacion:post'])]
    private ?string $matricula ;
    
    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    #[Assert\NotNull(groups: ['liquidacion:post'])]
    #[Assert\NotBlank(groups: ['liquidacion:post'])]
    #[Assert\Length(min: 1, max: 20, groups: ['liquidacion:post', 'liquidacion:put'])]
    private ?string $identificador = null;
    
    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    private ConceptoSubResource $concepto ;

    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    #[Assert\NotNull(groups: ['liquidacion:post'])]
    #[Assert\NotBlank(groups: ['liquidacion:post'])]
    private string $planPagoId ;

    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    #[Assert\NotNull(groups: ['liquidacion:post'])]
    #[Assert\NotBlank(groups: ['liquidacion:post'])]
    private int $numero ;
    
    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    #[Assert\NotNull(groups: ['liquidacion:post'])]
    #[Assert\NotBlank(groups: ['liquidacion:post'])]
    #[Assert\Length(min: 1, max: 10, groups: ['liquidacion:post', 'liquidacion:put'])]
    private float $importeOriginal ;
    
    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    #[Assert\NotNull(groups: ['liquidacion:post'])]
    #[Assert\NotBlank(groups: ['liquidacion:post'])]
    #[Assert\Length(min: 1, max: 10, groups: ['liquidacion:post', 'liquidacion:put'])]
    private float $ajuste ;
    
    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    #[Assert\NotNull(groups: ['liquidacion:post'])]
    #[Assert\NotBlank(groups: ['liquidacion:post'])]
    #[Assert\Length(min: 1, max: 10, groups: ['liquidacion:post', 'liquidacion:put'])]
    private float $interes ;
    
    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    private ?\DateTime $vencimiento ;
    
    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    #[Assert\NotNull(groups: ['liquidacion:post'])]
    #[Assert\NotBlank(groups: ['liquidacion:post'])]
    #[Assert\Length(min: 1, max: 10, groups: ['liquidacion:post', 'liquidacion:put'])]
    private float $importeTotal ;

    #[Groups(['liquidacion:read'])]
    private ?int $idSistemaAnterior ;


    public function __construct(){}

    public static function fromModel(Item $item){
        $resource = new self();
        $resource->setId($item->getId());
        $resource->setDescripcion($item->getDescripcion()->getValue());
        $resource->setMatricula($item->getMatricula() ? $item->getMatricula()->getValue() : null);
        $resource->setIdentificador($item->getIdentificador() ? $item->getIdentificador()->getValue(): null);
        $resource->setConcepto(ConceptoSubResource::fromModel($item->getConcepto()));
        $resource->setPlanPagoId($item->getPlanPagoId()->getValue());
        $resource->setNumero($item->getNumero()->getValue());
        $resource->setImporteOriginal($item->getImporteOriginal()->getValue());
        $resource->setAjuste($item->getAjuste()->getValue());
        $resource->setInteres($item->getInteres()->getValue());
        $resource->setVencimiento($item->getVencimiento() ? $item->getVencimiento()->getValue() : null);
        $resource->setImporteTotal($item->getImporteTotal()->getValue());
        $resource->setIdSistemaAnterior($item->getIdSistemaAnterior());

        return $resource ;

    }

    /**
     * Get the value of id
     */ 
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set the value of id
     *
     * @return  self
     */ 
    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Get the value of descripcion
     */ 
    public function getDescripcion()
    {
        return $this->descripcion;
    }

    /**
     * Set the value of descripcion
     *
     * @return  self
     */ 
    public function setDescripcion($descripcion)
    {
        $this->descripcion = $descripcion;

        return $this;
    }

    /**
     * Get the value of matricula
     */ 
    public function getMatricula()
    {
        return $this->matricula;
    }

    /**
     * Set the value of matricula
     *
     * @return  self
     */ 
    public function setMatricula($matricula)
    {
        $this->matricula = $matricula;

        return $this;
    }

    /**
     * Get the value of identificador
     */ 
    public function getIdentificador()
    {
        return $this->identificador;
    }

    /**
     * Set the value of identificador
     *
     * @return  self
     */ 
    public function setIdentificador($identificador)
    {
        $this->identificador = $identificador;

        return $this;
    }

    /**
     * Get the value of importeOriginal
     */ 
    public function getImporteOriginal()
    {
        return $this->importeOriginal;
    }

    /**
     * Set the value of importeOriginal
     *
     * @return  self
     */ 
    public function setImporteOriginal($importeOriginal)
    {
        $this->importeOriginal = $importeOriginal;

        return $this;
    }

    /**
     * Get the value of ajuste
     */ 
    public function getAjuste()
    {
        return $this->ajuste;
    }

    /**
     * Set the value of ajuste
     *
     * @return  self
     */ 
    public function setAjuste($ajuste)
    {
        $this->ajuste = $ajuste;

        return $this;
    }

    /**
     * Get the value of interes
     */ 
    public function getInteres()
    {
        return $this->interes;
    }

    /**
     * Set the value of interes
     *
     * @return  self
     */ 
    public function setInteres($interes)
    {
        $this->interes = $interes;

        return $this;
    }

    /**
     * Get the value of vencimiento
     */ 
    public function getVencimiento()
    {
        return $this->vencimiento;
    }

    /**
     * Set the value of vencimiento
     *
     * @return  self
     */ 
    public function setVencimiento($vencimiento)
    {
        $this->vencimiento = $vencimiento;

        return $this;
    }

    /**
     * Get the value of importeTotal
     */ 
    public function getImporteTotal()
    {
        return $this->importeTotal;
    }

    /**
     * Set the value of importeTotal
     *
     * @return  self
     */ 
    public function setImporteTotal($importeTotal)
    {
        $this->importeTotal = $importeTotal;

        return $this;
    }

    /**
     * Get the value of concepto
     */ 
    public function getConcepto()
    {
        return $this->concepto;
    }

    /**
     * Set the value of concepto
     *
     * @return  self
     */ 
    public function setConcepto($concepto)
    {
        $this->concepto = $concepto;

        return $this;
    }

    /**
     * Get the value of idSistemaAnterior
     */ 
    public function getIdSistemaAnterior()
    {
        return $this->idSistemaAnterior;
    }

    /**
     * Set the value of idSistemaAnterior
     *
     * @return  self
     */ 
    public function setIdSistemaAnterior($idSistemaAnterior)
    {
        $this->idSistemaAnterior = $idSistemaAnterior;

        return $this;
    }

    /**
     * Get the value of planPagoId
     */ 
    public function getPlanPagoId()
    {
        return $this->planPagoId;
    }

    /**
     * Set the value of planPagoId
     *
     * @return  self
     */ 
    public function setPlanPagoId($planPagoId)
    {
        $this->planPagoId = $planPagoId;

        return $this;
    }

    /**
     * Get the value of numero
     */ 
    public function getNumero()
    {
        return $this->numero;
    }

    /**
     * Set the value of numero
     *
     * @return  self
     */ 
    public function setNumero($numero)
    {
        $this->numero = $numero;

        return $this;
    }
}