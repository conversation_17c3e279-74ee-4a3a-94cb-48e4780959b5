<?php

declare(strict_types=1);

namespace App\Liquidacion\Infrastructure\ApiPlatform\Resource;

use App\Shared\Domain\Model\Concepto\Concepto;
use Symfony\Component\Serializer\Annotation\Groups;

final class ConceptoSubResource {
    
    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    private string $id ;

    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    private string $nombre ;

    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    private string $key ;

    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    private GestionMontoSubResource $gestionMonto ;

    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    private TipoItemSubResource $tipoItem ;

    #[Groups(['liquidacion:read', 'liquidacion:write'])]
    private int $signo = 1;

    public function __construct(){}


    public static function fromModel(Concepto $concepto){

        $resource = new self();

        $resource->setId($concepto->getId());
        $resource->setNombre($concepto->getNombre()->getValue());
        $resource->setKey($concepto->getKey()->getValue());
        $resource->setGestionMonto(GestionMontoSubResource::fromModel($concepto->getGestionMonto()));
        $resource->setTipoItem(TipoItemSubResource::fromModel($concepto->getTipoItem()));

        return $resource ;
    }

    /**
     * Get the value of id
     */ 
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set the value of id
     *
     * @return  self
     */ 
    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Get the value of nombre
     */ 
    public function getNombre()
    {
        return $this->nombre;
    }

    /**
     * Set the value of nombre
     *
     * @return  self
     */ 
    public function setNombre($nombre)
    {
        $this->nombre = $nombre;

        return $this;
    }

    /**
     * Get the value of key
     */ 
    public function getKey()
    {
        return $this->key;
    }

    /**
     * Set the value of key
     *
     * @return  self
     */ 
    public function setKey($key)
    {
        $this->key = $key;

        return $this;
    }

    /**
     * Get the value of gestionMonto
     */ 
    public function getGestionMonto()
    {
        return $this->gestionMonto;
    }

    /**
     * Set the value of gestionMonto
     *
     * @return  self
     */ 
    public function setGestionMonto($gestionMonto)
    {
        $this->gestionMonto = $gestionMonto;

        return $this;
    }

    /**
     * Get the value of tipoItem
     */ 
    public function getTipoItem()
    {
        return $this->tipoItem;
    }

    /**
     * Set the value of tipoItem
     *
     * @return  self
     */ 
    public function setTipoItem($tipoItem)
    {
        $this->tipoItem = $tipoItem;

        return $this;
    }

    /**
     * Get the value of signo
     */ 
    public function getSigno()
    {
        return $this->signo;
    }

    /**
     * Set the value of signo
     *
     * @return  self
     */ 
    public function setSigno($signo)
    {
        $this->signo = $signo;

        return $this;
    }
}
