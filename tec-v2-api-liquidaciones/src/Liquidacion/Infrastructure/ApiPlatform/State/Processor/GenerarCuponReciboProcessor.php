<?php

namespace App\Liquidacion\Infrastructure\ApiPlatform\State\Processor;

use App\Liquidacion\Infrastructure\DTO\Input\NotaCredito\NotaCreditoDTO;
use App\Liquidacion\Infrastructure\DTO\Input\Pago\PagoDTO;
use DateTime;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\Validator\ValidatorInterface;
use App\Core\Application\Command\CommandBusInterface;
use App\Core\Infrastructure\ApiPlatform\State\GenericAuditableProcessor;
use App\Liquidacion\Infrastructure\DTO\Output\ConceptosOutput;
use App\Core\Infrastructure\ApiPlatform\State\GenericProcessor;
use App\Liquidacion\Infrastructure\DTO\Output\ConceptoOutputDTO;
use App\Liquidacion\Infrastructure\DTO\Input\Concepto\ConceptoDTO;
use App\Liquidacion\Infrastructure\DTO\Input\TipoItem\TipoItemDTO;
use App\Shared\Infrastructure\Service\InfrastructureApplicationService;
use App\Liquidacion\Application\Command\GenerarCuponReciboManualCommand;
use App\Liquidacion\Infrastructure\DTO\Input\Cuenta\CuentaDTOInput;
use App\Liquidacion\Infrastructure\DTO\Input\GestionMonto\GestionMontoDTO;
use App\Liquidacion\Infrastructure\DTO\Input\Instrumento\InstrumentoDTO;
use App\Liquidacion\Infrastructure\DTO\Input\Matricula\MatriculaDTO;
use App\Liquidacion\Infrastructure\DTO\Input\Banco\BancoDTOInput;
use App\Shared\Application\Service\SharedHttpApplicationService;
use Symfony\Component\HttpFoundation\JsonResponse;

final class GenerarCuponReciboProcessor extends GenericAuditableProcessor{

    const TIPO_OTRO_CONCEPTO = "otro_concepto";
    const TIPO_BONIFICACION_MANUAL = "bonificacion_manual";
    private $data;

    public function __construct(
        private CommandBusInterface $commandBus,
        private ValidatorInterface $validator,
        private InfrastructureApplicationService $infraApplicationService,
        private SharedHttpApplicationService $httpService
    ) {
        parent::__construct($commandBus, $validator);
    }

    public function setCommand($data, Operation $operation, array $uriVariables = [], array $context = []): mixed
    {
        $this->data = $data;
        $items = $this->itemDTOtoArray($data);
        
        $pagos = $this->infraApplicationService->pagosDTOtoArray($data);

        $notas_creditos =$this->notasDTOtoArray($data);

        $command = new GenerarCuponReciboManualCommand(
            $data->getPersonaId(),
            $items,
            $pagos,
            $notas_creditos,
            $data->getFechaCalculo(),
            $data->getModo(),
            $data->getGenerar(),
            $data->getCircuito(),
            null, //$data->getSedeId()
            $data->getManual()
        );

        return $command;
    }

    public function getResource($model)
    {
        if(!$this->data->getGenerar()){
            $output = $this->convertirOutput($model['liquidacion'],$model['notas_credito'],$model['pagos'],$model['fecha_calculo'],$model['monto_total']);
        }else{
            
            $output = new ConceptosOutput();//generar mensaje de  correcto , si no hubo inconvenientes
            $output->setLiquidacionId($model["liquidacion"]->getId()->getValue());
            $output->setMessage("generado correctamente");
        }

        return $output ;
    }

    public function convertirOutput($liquidacion,$notas_credito,$pagos,$fecha_calculo,$monto_total){
        
        $output = new ConceptosOutput();

        $items = [];
        $monto_conceptos = 0;
        foreach($liquidacion['items'] as $item){            
            $matricula = $this->httpService->obtenerMatricula($item['matricula']);
            $concepto = $this->httpService->obtenerConcepto($item['concepto']['id']);
            
            $monto_conceptos += $item['importeTotal'] * $item['concepto']['signo'];

            if($concepto->tipo_item->key == self::TIPO_OTRO_CONCEPTO || $concepto->key == self::TIPO_BONIFICACION_MANUAL ){
                $id = null ;
            }else{
                $id = $item['id'] ;
            }
            
            $items [] = new ConceptoOutputDTO(
                $id,
                new MatriculaDTO(
                    $matricula->id,
                    isset($matricula->numero) ? $matricula->numero : 0,
                    isset($matricula->codigo) ? $matricula->codigo : null,
                    $matricula->titulo_id,
                    $matricula->institucion_id,
                    $matricula->categoria_id,
                    $matricula->persona->id
                ),
                $item['descripcion'],
                new ConceptoDTO(
                    $concepto->id,
                    $concepto->nombre,
                    $concepto->key,
                    $concepto->signo,
                    $concepto->autogenerado,
                    new GestionMontoDTO(
                        $concepto->gestion_monto->configuracion,
                        isset($concepto->gestion_monto->valor) ? $concepto->gestion_monto->valor : null  
                    ),
                    new TipoItemDTO(
                        $concepto->tipo_item->id,
                        $concepto->tipo_item->nombre,
                        $concepto->tipo_item->key,
                        $concepto->tipo_item->tipo
                    )    
                ), 
                $item['planPagoId'],
                $item['numero'],
                $item['importeOriginal'],
                $item['ajuste'],
                $item['interes'],
                $item['vencimiento'],
                $item['fecha'],
                $item['importeTotal']


            );
        }

        $notasCredito = [];
        $monto_notas = 0;
        foreach($notas_credito as $nc){
            $monto_notas += $nc['importe'];
            $notasCredito[] = new NotaCreditoDTO(
                $nc['id'],
                $nc['fecha'],
                $nc['descripcion'],
                $nc['importe']
            );
        }
 
        $pagosDTO = [];
        $monto_pagos = 0;
        foreach($pagos as $pago){
            $monto_pagos += $pago->importe_total; 
            $pagosDTO[] = new PagoDTO(
                $pago->descripcion,
                new InstrumentoDTO(
                    $pago->instrumento['id'],
                    $pago->instrumento['nombre'],
                    $pago->instrumento['key'],
                    $pago->instrumento['signo'],
                    new TipoItemDTO(
                        $pago->instrumento['tipo_item']['id'],
                        $pago->instrumento['tipo_item']['nombre'],
                        $pago->instrumento['tipo_item']['key'],
                        $pago->instrumento['tipo_item']['tipo']
                    )
                ),
                DateTime::createFromFormat('Y-m-d H:i:s', $pago->fecha_pago),
                $pago->cuenta ? new CuentaDTOInput(
                    $pago->cuenta['id'],
                    $pago->cuenta['nombre'],
                    $pago->cuenta['numero'],
                    $pago->cuenta['sucursal'],
                    new BancoDTOInput(
                        $pago->cuenta['banco']['id'],
                        $pago->cuenta['banco']['nombre'],
                    )
                ) : null,
                $pago->transaccion,
                $pago->operacion,
                $pago->importe_total
            );
        }

        $flags = $this->calculoFlagValidacion($monto_total,!empty($pagos));

        $output->setConceptos($items);
        $output->setNotasCredito($notasCredito);
        $output->setPagos($pagosDTO);
        $output->setMontoConceptos($monto_conceptos - $monto_notas);
        $output->setMontoPagos($monto_pagos);
        $output->setMontoTotal($monto_total);
        $output->setRecibo($flags['recibo']);
        $output->setCupon($flags['cupon']);
        $output->setNotaCredito($flags['notaCredito']);
        $output->setFechaCalculo($fecha_calculo);

        return $output ;
        
    }

    /**
     * Metodo que permite calcular flags para facilitarle a front la muestra de mensajes 
     */
    public function calculoFlagValidacion($monto,$pagos){

        $recibo = false ;
        $cupon = false ;
        $notaCredito = false ;
        
        //si es cupon y el monto es > 0 , permite crear el cupon 
        if(!$pagos & ($monto > 0) ){
            $cupon = true ;    
        }

        //si exiten pagos y el monto es (-) permite crear recibo y nota 
        if($pagos & ($monto < 0) ){
            $recibo = true ;
            $notaCredito = true ;
        }
        
        //si exiten pagos y el total es 0 permite crear recibo 
        if($pagos & ($monto == 0)){
            $recibo = true ;
        }

        return [
            "recibo" => $recibo ,
            "cupon" => $cupon,
            "notaCredito" => $notaCredito
        ];
    }


    public function itemDTOtoArray($data){

        $items  = [] ;
        foreach ($data->getConceptos() as $item ){
            $items[] = (object)[
                "id" => $item->getId(),
                "matricula" => (object)[
                    "id" => $item->getMatricula()->getId(),
                    "numero" => $item->getMatricula()->getNumero(),
                    "codigo" => $item->getMatricula()->getCodigo(),
                    "titulo_id" => $item->getMatricula()->getTituloId(),
                    "institucion_id" => $item->getMatricula()->getInstitucionId(),
                    "categoria_id" => $item->getMatricula()->getCategoriaId(),
                    "persona_id" => $item->getMatricula()->getPersonaId()
                ],
                "descripcion" => $item->getDescripcion(),
                "concepto" => (object)[
                    "key" => $item->getConcepto()->getKey(),
                    "tipo_item" => (object) [
                        "key" => $item->getConcepto()->getTipoItem()->getKey()
                    ]
                ],
                "plan_pago_id" => $item->getPlanPagoId(),
                "numero" => $item->getNumero(),
                "importe" => $item->getImporteOriginal(),
                "ajuste" => $item->getAjuste(),
                "interes" => $item->getInteres(),
                "fecha_vencimiento" => $item->getVencimiento()->format('Y-m-d H:i:s'),
                "importe_total" => $item->getImporteTotal(),
                "fecha" => $item->getFecha(),
            ];
        }

        return $items ;
    }

    public function notasDTOtoArray($data){
        $notas = [];
        
        foreach($data->getNotasCredito() as $nota){
            $notas[] = [
                "id" => $nota->getId(),
                "fecha" => $nota->getFecha(),
                "descripcion" => $nota->getDescripcion(),
                "importe" => $nota->getImporte()
            ];
        }

        return $notas ;
    }
    
}