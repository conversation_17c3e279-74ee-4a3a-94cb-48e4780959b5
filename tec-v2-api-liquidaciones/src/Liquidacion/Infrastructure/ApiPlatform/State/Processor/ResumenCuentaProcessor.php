<?php


namespace App\Liquidacion\Infrastructure\ApiPlatform\State\Processor;

use DateTime;
use ApiPlatform\Metadata\Operation;
use App\Liquidacion\Application\Command\ResumenCuentaCommand;
use App\Liquidacion\Infrastructure\DTO\Output\ItemResumenDTO;
use App\Core\Infrastructure\ApiPlatform\State\GenericProcessor;
use App\Liquidacion\Infrastructure\DTO\Output\ResumenCuentaOutput;

final class ResumenCuentaProcessor extends GenericProcessor{

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = [])
    {
        $command = new ResumenCuentaCommand($data); 

        $result = $this->dispatch($command);

        $items= [];
        foreach($result as $r){
            $items [] = new ItemResumenDTO(
                $r['codigo_matricula'],
                $r['estado'] ,
                DateTime::createFromFormat('Y-m-d',substr($r['fecha_vencimiento'], 0, 10)),
                $r['descripcion'],
                $r['importe_total'],
                $r['recibo_id'],
                isset($r['fecha_pago']) ? DateTime::createFromFormat('Y-m-d',substr($r['fecha_pago'], 0, 10)) : null,
                isset($r['orden_pago_id']) ? $r['orden_pago_id'] : null,
                isset($r['type']) ? $r['type'] : null 
            );
        }
        return new ResumenCuentaOutput(
            $items
        );
    }
}