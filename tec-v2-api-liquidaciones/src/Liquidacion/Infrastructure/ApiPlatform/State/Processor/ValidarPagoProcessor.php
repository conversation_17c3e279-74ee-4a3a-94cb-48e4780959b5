<?php

namespace App\Liquidacion\Infrastructure\ApiPlatform\State\Processor;

use Symfony\Component\Uid\Uuid;
use ApiPlatform\Metadata\Operation;
use App\Liquidacion\Application\Command\ValidarPagoCommand;
use App\Core\Infrastructure\ApiPlatform\State\GenericProcessor;
use App\Liquidacion\Infrastructure\DTO\Output\ValidarPagoOutput;
use App\Core\Infrastructure\ApiPlatform\State\GenericAuditableProcessor;

final class ValidarPagoProcessor extends GenericAuditableProcessor {

    public function setCommand($data, Operation $operation, array $uriVariables = [], array $context = []): mixed
    {
        $pago = $this->InputToArray($data);

        $command = new ValidarPagoCommand($pago);

        return $command;
    }

    public function getResource($model)
    {
        $output = new ValidarPagoOutput();
        $output->setValido($model['valido']);
        $output->setMessage($model['message']);
        $output->setNotaCredito($model['nota_credito']);
        $output->setMontoNota($model['monto_nota']);
        $output->setReciboId($model['reciboId']);
        $output->setNumeroRecibo($model['numeroRecibo']);
        $output->setItemsValidos($model['items_validos']);
        
        return $output ;
    }

    public function InputToArray($data){

        $items = [];
        foreach($data->getItems() as $item){
            $items[] = (object)[
                "descripcion" => $item->getDescripcion(),
                "instrumento" => (object)[
                    "id" => $item->getInstrumento()->getId(),
                    "nombre" => $item->getInstrumento()->getNombre(),
                    "key" => $item->getInstrumento()->getKey(),
                    "signo" => $item->getInstrumento()->getSigno(),
                    "tipo_item" => (object)[
                        "id" => $item->getInstrumento()->getTipoItem()->getId(),
                        "nombre" => $item->getInstrumento()->getTipoItem()->getNombre(),
                        "key" => $item->getInstrumento()->getTipoItem()->getKey(),
                        "tipo" => $item->getInstrumento()->getTipoItem()->getTipo(),
                    ]
                ],
                "fecha_pago" => $item->getFechaPago()->format('Y-m-d'),
                "cuenta" => $item->getCuenta(),
                "transaccion" => $item->getTransaccion(),
                "operacion" => $item->getOperacion(),
                "importe_total" => $item->getImporteTotal()
            ];
        }

        $pago = [
            "id" => Uuid::v4()->__toString(),
            "microservicio" => $data->getMicroservicio(),
            "agente" => $data->getAgente(),
            "monto_total" => $data->getMontoTotal(),
            "items" => $items ,
            "fecha_pago" => $data->getFechaPago()->format('Y-m-d'),
            "fecha" => $data->getFecha()->format('Y-m-d'),
            "orden_pago_id" => $data->getOrdenPagoId(),
            "recaudador_id" => $data->getRecaudadorId(),
            "recibo_id" => $data->getReciboId()
        ];

        return $pago ;
    }
}