<?php

namespace App\Liquidacion\Infrastructure\ApiPlatform\State\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\Shared\Domain\Model\Persona\PersonaId;
use App\Core\Application\Query\QueryBusInterface;
use App\Liquidacion\Application\Query\FindPreLiquidacionQuery;
use App\Core\Infrastructure\ApiPlatform\State\GenericItemProvider;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use App\Shared\Infrastructure\Service\InfrastructureApplicationService;

final class PreLiquidacionItemProvider implements ProviderInterface{
    
    public function __construct(
        private InfrastructureApplicationService $infraApplicationService,
        private QueryBusInterface $queryBus
    ){}

    public function getId($uriVariables)
    {   
        return [
            "id"   =>  $uriVariables["id"],
            "fecha_calculo" =>  @$uriVariables["filters"] ? @$uriVariables["filters"]["fecha_calculo"] : null
        ];
    }

    public function getQuery($data)
    {   
        return new FindPreLiquidacionQuery($data['id'],$data['fecha_calculo']);
    }

    public function getResource($models){

        $itemOutput = $this->infraApplicationService->objectToItemOutput($models);
        
        return $itemOutput ;
        
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        try {
            $uriVariables['filters'] = @$context['filters'];
            $id = $this->getId($uriVariables);
        } catch (\InvalidArgumentException $e) {
            throw new BadRequestException($e->getMessage());
        }
        
        $model = $this->queryBus->ask( $this->getQuery($id) );

        return null !== $model ? $this->getResource($model) : null;
    }

}
